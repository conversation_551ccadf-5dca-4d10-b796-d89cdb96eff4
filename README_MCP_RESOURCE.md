# MCP Resource 智能数据获取系统

## 概述

基于Google ADK框架和fastmcp的mcp_resource注解，实现了一个智能的数据获取系统，能够根据用户的不同请求自动选择最合适的数据获取方式（文件、数据库、HTTP API）。

## 🚀 核心功能

### 1. 智能资源路由
- **自动分析用户请求**：通过关键词匹配识别数据需求类型
- **智能选择资源类型**：自动决定使用文件、数据库还是HTTP API
- **置信度评估**：为每种选择提供置信度分数
- **Fallback机制**：主要方式失败时自动尝试备用方案

### 2. 多种资源类型支持
- **文件资源** (`file://`): 读取本地文件（markdown、json、txt等）
- **数据库资源** (`db://`): 执行SQLite查询
- **HTTP资源** (`http://`, `https://`): 调用外部API

### 3. MCP Resource注解
使用fastmcp的mcp_resource注解实现标准化的资源访问：

```python
@mcp.resource("file://{path}")
async def read_file_resource(path: str) -> str:
    """读取文件资源"""

@mcp.resource("db://{db_path}?query={query}")
async def query_database_resource(db_path: str, query: str) -> str:
    """查询数据库资源"""

@mcp.resource("http://{url}")
async def fetch_http_resource(url: str) -> str:
    """获取HTTP资源"""
```

## 📁 文件结构

```
├── resources.py              # MCP Resource服务器（使用mcp_resource注解）
├── resource_tools.py         # MCP工具服务器（提供具体的工具函数）
├── resource_config.py        # 智能路由配置和规则
├── multi_tool_agent/
│   └── agent.py             # 增强的Agent配置（集成资源获取功能）
├── test_basic_resources.py  # 基础功能测试
├── demo_agent_usage.py      # 完整使用演示
└── README_MCP_RESOURCE.md   # 本文档
```

## 🔧 核心组件

### 1. ResourceManager (resources.py)
统一的资源管理器，提供：
- 文件内容读取和解析
- SQLite数据库查询
- HTTP请求处理
- 错误处理和异常管理

### 2. 智能路由系统 (resource_config.py)
- **数据类别枚举**：企业信息、报警记录、风险评估、作业活动等
- **路由规则**：基于关键词的智能匹配
- **策略配置**：每种数据类别的资源获取策略

### 3. MCP工具集成 (resource_tools.py)
提供标准的MCP工具：
- `read_data_file`: 读取data目录下的数据文件
- `query_local_database`: 查询本地SQLite数据库
- `fetch_web_api`: 调用Web API
- `get_resource_info`: 获取资源元信息

### 4. 增强的Agent (multi_tool_agent/agent.py)
- 集成智能资源获取功能
- 支持多种工具的协同工作
- 智能决策和错误处理

## 🎯 使用场景

### 场景1：报警记录查询
```
用户请求: "查看合全药业的报警记录"
↓
关键词分析: ["报警", "合全"]
↓
路由决策: alarm_record → file资源
↓
执行: read_data_file("alarm_record.md")
↓
结果: 返回JSON格式的报警数据
```

### 场景2：风险等级评估
```
用户请求: "获取天辰化工的风险等级"
↓
关键词分析: ["风险", "天辰"]
↓
路由决策: risk_assessment → file资源
↓
执行: read_data_file("risk_level.md")
↓
结果: 返回风险等级数据
```

### 场景3：外部API调用
```
用户请求: "获取当前天气信息"
↓
关键词分析: ["天气", "外部"]
↓
路由决策: external_api → http资源
↓
执行: fetch_web_api("http://api.weather.com/current")
↓
结果: 返回天气API数据
```

## 🚀 快速开始

### 1. 启动资源服务器
```bash
# 启动MCP Resource服务器
python resources.py

# 启动MCP工具服务器
python resource_tools.py
```

### 2. 配置Agent
在agent配置中添加资源工具集：

```python
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

resource_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./resource_tools.py"],
        timeout=120.0
    ),
    tool_filter=['read_data_file', 'query_local_database', 'fetch_web_api']
)
```

### 3. 运行测试
```bash
# 基础功能测试
python test_basic_resources.py

# 完整演示
python demo_agent_usage.py
```

## 📊 测试结果

✅ **文件资源读取** - 成功解析markdown中的JSON数据
- alarm_record.md: 10条记录
- job_reporting_info.md: 19条记录  
- risk_level.md: 8条记录

✅ **数据库资源查询** - 支持SQLite查询和事务处理

✅ **智能路由功能** - 根据关键词自动选择合适的资源类型

✅ **MCP Resource模式** - 支持多种URI格式的资源访问

## 🔄 工作流程

1. **接收用户请求** - Agent接收自然语言请求
2. **请求分析与路由** - 分析关键词，确定数据类别和资源类型
3. **选择合适的工具** - 根据分析结果选择MCP工具
4. **执行资源获取** - 调用MCP工具获取数据
5. **处理和响应** - 处理数据并生成用户友好的响应

## 🎯 扩展建议

### 1. 添加新的资源类型
- Redis缓存资源
- MongoDB文档数据库
- Elasticsearch搜索引擎
- 消息队列资源

### 2. 增强路由规则
- 基于机器学习的意图识别
- 上下文感知的路由决策
- 用户偏好学习

### 3. 性能优化
- 资源缓存机制
- 并发请求处理
- 连接池管理

## 📝 配置说明

### 路由规则配置
在`resource_config.py`中可以调整：
- 关键词匹配规则
- 置信度阈值
- 资源优先级
- Fallback策略

### 数据源配置
支持的数据源：
- 本地文件（./data/目录）
- SQLite数据库
- HTTP/HTTPS API
- 可扩展其他数据源

## 🔒 安全考虑

- 文件访问权限验证
- SQL注入防护
- HTTP请求超时控制
- 错误信息脱敏

## 📞 技术支持

如有问题或建议，请参考：
- 测试文件中的示例用法
- demo_agent_usage.py中的完整演示
- resource_config.py中的配置说明
