#!/usr/bin/env python3
"""
SQLite数据库初始化脚本
创建企业管理相关的数据库表并导入初始数据
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, db_path: str = "./enterprise.db"):
        self.db_path = db_path
        self.data_dir = Path("./data")
    
    def create_database(self):
        """创建数据库和所有表"""
        print(f"🚀 开始初始化数据库: {self.db_path}")
        
        # 删除已存在的数据库文件
        if Path(self.db_path).exists():
            Path(self.db_path).unlink()
            print(f"🗑️  删除已存在的数据库文件")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建所有表
            self.create_enterprises_table(cursor)
            self.create_alarm_records_table(cursor)
            self.create_risk_levels_table(cursor)
            self.create_work_activities_table(cursor)
            self.create_major_hazards_table(cursor)
            self.create_startup_shutdown_table(cursor)
            
            # 导入数据
            self.import_data_from_files(cursor)
            
            conn.commit()
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            conn.rollback()
            print(f"❌ 数据库初始化失败: {e}")
            raise
        finally:
            conn.close()
    
    def create_enterprises_table(self, cursor):
        """创建企业信息表"""
        cursor.execute('''
            CREATE TABLE enterprises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                short_name TEXT,
                area TEXT,
                location TEXT,
                contact_person TEXT,
                contact_phone TEXT,
                risk_level TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("📋 创建企业信息表 (enterprises)")
    
    def create_alarm_records_table(self, cursor):
        """创建报警记录表"""
        cursor.execute('''
            CREATE TABLE alarm_records (
                id INTEGER PRIMARY KEY,
                enterprise_name TEXT NOT NULL,
                alarm_content TEXT,
                alarm_area TEXT,
                alarm_time TIMESTAMP,
                recovery_time TIMESTAMP,
                is_valid BOOLEAN,
                alarm_type TEXT,
                alarm_status TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("🚨 创建报警记录表 (alarm_records)")
    
    def create_risk_levels_table(self, cursor):
        """创建风险等级表"""
        cursor.execute('''
            CREATE TABLE risk_levels (
                id INTEGER PRIMARY KEY,
                enterprise_name TEXT NOT NULL,
                control_object TEXT,
                risk_analysis_object_code TEXT,
                risk_level TEXT,
                responsible_department TEXT,
                responsible_person TEXT,
                is_high_risk BOOLEAN,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("⚠️  创建风险等级表 (risk_levels)")
    
    def create_work_activities_table(self, cursor):
        """创建作业活动表"""
        cursor.execute('''
            CREATE TABLE work_activities (
                id INTEGER PRIMARY KEY,
                plan_number TEXT,
                enterprise_name TEXT NOT NULL,
                activity_content TEXT,
                work_area TEXT,
                workers_count INTEGER,
                work_type TEXT,
                risk_level TEXT,
                data_source TEXT,
                apply_time TIMESTAMP,
                planned_period TEXT,
                worker_category TEXT,
                formula TEXT,
                remark TEXT,
                is_high_risk BOOLEAN,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("🔧 创建作业活动表 (work_activities)")
    
    def create_major_hazards_table(self, cursor):
        """创建重大危险源表"""
        cursor.execute('''
            CREATE TABLE major_hazards (
                id INTEGER PRIMARY KEY,
                enterprise_name TEXT NOT NULL,
                risk_source_name TEXT,
                risk_source_short_name TEXT,
                risk_source_code TEXT,
                risk_source_level TEXT,
                area TEXT,
                location_type TEXT,
                chemicals_involved TEXT,  -- JSON格式存储
                r_value REAL,
                is_high_level BOOLEAN,
                record_date DATE,
                valid_period TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("☢️  创建重大危险源表 (major_hazards)")
    
    def create_startup_shutdown_table(self, cursor):
        """创建开停车记录表"""
        cursor.execute('''
            CREATE TABLE startup_shutdown_logs (
                id INTEGER PRIMARY KEY,
                enterprise_name TEXT NOT NULL,
                responsible_person TEXT,
                contact TEXT,
                operation_type TEXT,
                current_status TEXT,
                is_interlock_removed BOOLEAN,
                start_time TIMESTAMP,
                planned_completion_time TIMESTAMP,
                facility_name TEXT,
                area_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("🔄 创建开停车记录表 (startup_shutdown_logs)")
    
    def import_data_from_files(self, cursor):
        """从markdown文件导入数据"""
        print("\n📥 开始导入数据...")
        
        # 导入报警记录
        self.import_alarm_records(cursor)
        
        # 导入风险等级
        self.import_risk_levels(cursor)
        
        # 导入作业活动
        self.import_work_activities(cursor)
        
        # 导入重大危险源
        self.import_major_hazards(cursor)
        
        # 导入开停车记录
        self.import_startup_shutdown_logs(cursor)
        
        # 导入企业信息
        self.import_enterprises(cursor)
    
    def load_json_from_md(self, file_name: str) -> List[Dict]:
        """从markdown文件中提取JSON数据"""
        file_path = self.data_dir / file_name
        if not file_path.exists():
            print(f"⚠️  文件不存在: {file_name}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取JSON数据
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            
            if start_idx == -1 or end_idx == 0:
                return []
            
            json_str = content[start_idx:end_idx]
            data = json.loads(json_str)
            print(f"📄 从 {file_name} 加载了 {len(data)} 条记录")
            return data
            
        except Exception as e:
            print(f"❌ 加载 {file_name} 失败: {e}")
            return []
    
    def import_alarm_records(self, cursor):
        """导入报警记录"""
        data = self.load_json_from_md("alarm_record.md")
        if not data:
            return
        
        for record in data:
            cursor.execute('''
                INSERT INTO alarm_records 
                (id, enterprise_name, alarm_content, alarm_area, alarm_time, 
                 recovery_time, is_valid, alarm_type, alarm_status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('id'),
                record.get('enterprise_name'),
                record.get('alarm_content'),
                record.get('alarm_area'),
                record.get('alarm_time'),
                record.get('recovery_time'),
                record.get('is_valid'),
                record.get('alarm_type'),
                record.get('alarm_status')
            ))
        print(f"✅ 导入 {len(data)} 条报警记录")
    
    def import_risk_levels(self, cursor):
        """导入风险等级"""
        data = self.load_json_from_md("risk_level.md")
        if not data:
            return
        
        for record in data:
            cursor.execute('''
                INSERT INTO risk_levels 
                (id, enterprise_name, control_object, risk_analysis_object_code,
                 risk_level, responsible_department, responsible_person, is_high_risk)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('id'),
                record.get('enterprise_name'),
                record.get('control_object'),
                record.get('risk_analysis_object_code'),
                record.get('risk_level'),
                record.get('responsible_department'),
                record.get('responsible_person'),
                record.get('is_high_risk')
            ))
        print(f"✅ 导入 {len(data)} 条风险等级记录")
    
    def import_work_activities(self, cursor):
        """导入作业活动"""
        data = self.load_json_from_md("job_reporting_info.md")
        if not data:
            return
        
        for record in data:
            cursor.execute('''
                INSERT INTO work_activities 
                (id, plan_number, enterprise_name, activity_content, work_area,
                 workers_count, work_type, risk_level, data_source, apply_time,
                 planned_period, worker_category, formula, remark, is_high_risk)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('id'),
                record.get('plan_number'),
                record.get('enterprise_name'),
                record.get('activity_content'),
                record.get('work_area'),
                record.get('workers_count'),
                record.get('work_type'),
                record.get('risk_level'),
                record.get('data_source'),
                record.get('apply_time'),
                record.get('planned_period'),
                record.get('worker_category'),
                record.get('formula'),
                record.get('remark'),
                record.get('is_high_risk')
            ))
        print(f"✅ 导入 {len(data)} 条作业活动记录")
    
    def import_major_hazards(self, cursor):
        """导入重大危险源"""
        data = self.load_json_from_md("major_hazard_sources.md")
        if not data:
            return
        
        for record in data:
            chemicals = json.dumps(record.get('chemicals_involved', []), ensure_ascii=False)
            cursor.execute('''
                INSERT INTO major_hazards 
                (id, enterprise_name, risk_source_name, risk_source_short_name,
                 risk_source_code, risk_source_level, area, location_type,
                 chemicals_involved, r_value, is_high_level, record_date, valid_period)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('id'),
                record.get('enterprise_name'),
                record.get('risk_source_name'),
                record.get('risk_source_short_name'),
                record.get('risk_source_code'),
                record.get('risk_source_level'),
                record.get('area'),
                record.get('location_type'),
                chemicals,
                record.get('r_value'),
                record.get('is_high_level'),
                record.get('record_date'),
                record.get('valid_period')
            ))
        print(f"✅ 导入 {len(data)} 条重大危险源记录")
    
    def import_startup_shutdown_logs(self, cursor):
        """导入开停车记录"""
        data = self.load_json_from_md("startup_shutdown_log.md")
        if not data:
            return
        
        for record in data:
            cursor.execute('''
                INSERT INTO startup_shutdown_logs 
                (id, enterprise_name, responsible_person, contact, operation_type,
                 current_status, is_interlock_removed, start_time, planned_completion_time,
                 facility_name, area_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.get('id'),
                record.get('enterprise_name'),
                record.get('responsible_person'),
                record.get('contact'),
                record.get('operation_type'),
                record.get('current_status'),
                record.get('is_interlock_removed'),
                record.get('start_time'),
                record.get('planned_completion_time'),
                record.get('facility_name'),
                record.get('area_name')
            ))
        print(f"✅ 导入 {len(data)} 条开停车记录")
    
    def import_enterprises(self, cursor):
        """导入企业信息（从其他表中提取）"""
        # 从已有数据中提取企业信息
        cursor.execute('''
            INSERT INTO enterprises (name, short_name, risk_level)
            SELECT DISTINCT enterprise_name, 
                   CASE 
                       WHEN enterprise_name LIKE '%合全%' THEN '合全'
                       WHEN enterprise_name LIKE '%新阳%' THEN '新阳'
                       WHEN enterprise_name LIKE '%天辰%' THEN '天辰'
                       WHEN enterprise_name LIKE '%宏远%' THEN '宏远'
                       WHEN enterprise_name LIKE '%华兴%' THEN '华兴'
                       WHEN enterprise_name LIKE '%天智辰业%' THEN '天智辰业'
                       WHEN enterprise_name LIKE '%天伟%' THEN '天伟'
                       WHEN enterprise_name LIKE '%天能%' THEN '天能'
                       WHEN enterprise_name LIKE '%天业%' THEN '天业'
                       WHEN enterprise_name LIKE '%天域%' THEN '天域'
                       ELSE SUBSTR(enterprise_name, 1, 4)
                   END as short_name,
                   '中风险' as default_risk_level
            FROM alarm_records 
            WHERE enterprise_name IS NOT NULL AND enterprise_name != ''
        ''')
        
        count = cursor.rowcount
        print(f"✅ 自动生成 {count} 条企业信息记录")
    
    def show_database_info(self):
        """显示数据库信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        print(f"\n📊 数据库信息 ({self.db_path}):")
        print("-" * 50)
        
        tables = [
            'enterprises', 'alarm_records', 'risk_levels', 
            'work_activities', 'major_hazards', 'startup_shutdown_logs'
        ]
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"📋 {table}: {count} 条记录")
        
        conn.close()

def main():
    """主函数"""
    print("🚀 SQLite数据库初始化工具")
    print("=" * 50)
    
    # 初始化数据库
    initializer = DatabaseInitializer()
    initializer.create_database()
    
    # 显示数据库信息
    initializer.show_database_info()
    
    print("\n✅ 数据库初始化完成！")
    print(f"📁 数据库文件: {initializer.db_path}")
    print("\n🔧 可以使用以下方式连接数据库:")
    print(f"   sqlite3 {initializer.db_path}")
    print("\n📝 示例查询:")
    print("   SELECT * FROM enterprises;")
    print("   SELECT * FROM alarm_records WHERE enterprise_name LIKE '%合全%';")
    print("   SELECT enterprise_name, COUNT(*) FROM alarm_records GROUP BY enterprise_name;")

if __name__ == "__main__":
    main()
