# Agent 使用 MCP Resource 完整指南

## 概述

本指南展示如何在 Agent 中真正利用 MCP Resource 来处理用户查询，如"查一下天辰化工7月23的特殊作业"和"获取当前天气信息"。

## 🎯 核心实现

### 1. Agent 配置

在 `multi_tool_agent/agent.py` 中添加 MCP Resource 支持：

```python
# MCP Resource 智能路由代理
mcp_resource_agent = LlmAgent(
    model=LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name='mcp_resource_agent',
    description='MCP Resource智能路由助手，通过语义化URI访问企业数据资源',
    instruction=f'''你是一个MCP Resource智能路由助手，能够根据用户请求自动选择合适的资源URI进行数据访问。

**可用的MCP资源URI模式：**
1. **企业相关资源：**
   - enterprise://info/{{企业名}} - 获取企业基础信息
   - enterprise://alarms/{{企业名}} - 获取企业报警记录
   - enterprise://risks/{{企业名}} - 获取企业风险评估

2. **数据文件资源：**
   - data://file/job_reporting_info.md - 作业申报信息文件
   - data://file/alarm_record.md - 报警记录文件

3. **数据库查询资源：**
   - db://query/work_activities - 作业活动表
   - db://query/enterprises - 企业表

4. **外部API资源：**
   - weather://current/{{位置}} - 当前天气信息

**智能路由规则：**
- 特殊作业查询 → db://query/work_activities 或 data://file/job_reporting_info.md
- 天气查询 → weather://current/{{位置}}
- 企业信息查询 → enterprise://info/{{企业名}}
''',
    tools=[mcp_resource_server]
)

# 创建 AgentTool 包装器
mcp_resource_tool = AgentTool(agent=mcp_resource_agent)

# 在 root_agent 中使用
root_agent = LlmAgent(
    tools=[
        mcp_resource_tool,  # 优先使用 MCP Resource
        # 其他传统工具作为备用
    ]
)
```

### 2. MCP Resource 服务器

在 `resources.py` 中定义资源：

```python
@mcp.resource("enterprise://info/{enterprise_name}")
async def get_enterprise_info(enterprise_name: str) -> str:
    """获取企业基础信息资源"""

@mcp.resource("db://query/{table}")
async def query_table_resource(table: str) -> str:
    """查询数据库表资源"""

@mcp.resource("weather://current/{location}")
async def get_current_weather(location: str) -> str:
    """获取当前天气信息资源"""
```

## 🔍 具体查询处理

### 查询1：天辰化工7月23日的特殊作业

**用户输入：** "查一下天辰化工7月23日的特殊作业"

**Agent 处理流程：**

1. **请求分析**：
   - 识别企业名：天辰化工
   - 识别日期：7月23日
   - 识别查询类型：特殊作业

2. **资源选择**：
   - 主要资源：`db://query/work_activities`
   - 备用资源：`data://file/job_reporting_info.md`

3. **查询执行**：
   ```sql
   SELECT * FROM work_activities 
   WHERE enterprise_name LIKE '%天辰化工%' 
   AND DATE(apply_time) = '2025-07-23'
   ```

4. **结果返回**：
   ```json
   {
     "resource_type": "database_query",
     "data": [
       {
         "enterprise_name": "天辰化工有限公司",
         "activity_content": "18号C23.2F玻璃釜管道安装",
         "work_area": "氯乙烯一期精馏生产单元",
         "work_type": "二级动火",
         "risk_level": "未知风险",
         "apply_time": "2025-07-23 14:49:00",
         "is_high_risk": true
       }
     ]
   }
   ```

5. **Agent 响应**：
   ```
   我已经查询了天辰化工7月23日的特殊作业记录。

   📋 作业详情：
   - 企业：天辰化工有限公司
   - 作业内容：18号C23.2F玻璃釜管道安装
   - 作业区域：氯乙烯一期精馏生产单元
   - 作业类型：二级动火
   - 风险等级：未知风险（标记为高风险）
   - 申请时间：2025-07-23 14:49:00

   ⚠️ 风险提示：该作业为高风险作业，请确保安全措施到位。
   ```

### 查询2：获取当前天气信息

**用户输入：** "获取当前天气信息"

**Agent 处理流程：**

1. **请求分析**：
   - 识别查询类型：天气信息
   - 默认位置：常州

2. **资源选择**：
   - 资源URI：`weather://current/常州`

3. **查询执行**：
   - 调用天气API（当前为模拟数据）

4. **结果返回**：
   ```json
   {
     "resource_type": "weather_info",
     "location": "常州",
     "data": {
       "temperature": "25°C",
       "humidity": "65%",
       "weather_condition": "晴朗",
       "air_quality": "良好"
     }
   }
   ```

5. **Agent 响应**：
   ```
   我已经获取了当前的天气信息。

   🌤️ 天气状况：
   - 位置：常州
   - 温度：25°C
   - 湿度：65%
   - 天气：晴朗
   - 空气质量：良好

   ⚠️ 注意：当前为模拟数据，实际使用需要配置真实的天气API。
   ```

## 🔧 智能路由机制

### 1. 关键词匹配

```python
def analyze_user_request(user_request: str):
    if "特殊作业" in user_request and "7月23" in user_request:
        return "db://query/work_activities"
    elif "天气" in user_request:
        return "weather://current/常州"
    elif "企业信息" in user_request:
        return "enterprise://info/{企业名}"
```

### 2. 企业名称映射

```python
enterprise_mapping = {
    "天辰化工": "天辰化工有限公司",
    "合全药业": "常州合全药业有限公司",
    "新阳科技": "新阳科技集团有限公司"
}
```

### 3. Fallback 策略

```python
# 主要资源失败时的备用方案
fallback_strategies = {
    "db://query/work_activities": "data://file/job_reporting_info.md",
    "enterprise://alarms/{name}": "data://file/alarm_record.md",
    "weather://current/{location}": "提示用户配置天气API"
}
```

## 🚀 部署和使用

### 1. 启动 MCP Resource 服务器

```bash
# 启动资源服务器
python resources.py
```

### 2. 测试 Agent 功能

```bash
# 运行演示
python demo_agent_mcp_resource.py

# 运行集成测试
python test_agent_with_mcp_resource.py
```

### 3. 实际使用

```python
# 在你的应用中使用
from multi_tool_agent.agent import root_agent

# 处理用户查询
response = await root_agent.process("查一下天辰化工7月23日的特殊作业")
```

## 📊 支持的查询类型

### 1. 企业相关查询
- "查看合全药业的基本信息"
- "合全药业最近有什么报警吗？"
- "分析天辰化工的风险状况"

### 2. 作业活动查询
- "查一下天辰化工7月23日的特殊作业"
- "今天有哪些高风险作业？"
- "查询动火作业记录"

### 3. 外部信息查询
- "获取当前天气信息"
- "常州的天气怎么样？"

### 4. 综合分析查询
- "给我一个企业综合报告"
- "当前的风险态势如何？"

## 🎯 核心优势

### 1. 语义化访问
- URI 本身就描述了资源的含义
- 不需要记忆复杂的工具名称和参数

### 2. 智能路由
- Agent 自动分析用户请求
- 选择最合适的资源URI
- 支持 Fallback 策略

### 3. 统一协议
- 所有资源都通过 MCP 协议访问
- 标准化的数据返回格式

### 4. 易于扩展
- 添加新资源只需要添加新的 `@mcp.resource` 函数
- 不需要修改 Agent 代码

## 🔄 扩展建议

### 1. 添加更多资源类型
```python
@mcp.resource("maintenance://schedule/{enterprise_name}")
async def get_maintenance_schedule(enterprise_name: str) -> str:
    """获取维护计划资源"""

@mcp.resource("safety://inspection/{area}")
async def get_safety_inspection(area: str) -> str:
    """获取安全检查资源"""
```

### 2. 增强智能路由
- 使用机器学习进行意图识别
- 支持更复杂的日期解析
- 添加上下文感知能力

### 3. 性能优化
- 实现资源缓存机制
- 添加并发查询支持
- 优化数据库查询性能

## ✅ 验证清单

- [x] Agent 能够智能分析用户请求
- [x] 自动选择合适的 MCP Resource URI
- [x] 支持特定日期的作业查询
- [x] 支持天气信息查询
- [x] 实现 Fallback 策略
- [x] 提供结构化的响应格式
- [x] 集成到现有的 Agent 工作流

现在你的 Agent 可以真正利用 MCP Resource 来智能处理各种用户查询了！
