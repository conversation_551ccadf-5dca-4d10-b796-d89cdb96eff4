# SQLite数据库初始化和集成指南

## 概述

为MCP Resource系统创建了完整的SQLite数据库，包含企业管理相关的所有数据表，并实现了智能的文件/数据库资源选择机制。

## 🗄️ 数据库结构

### 数据库文件
- **文件名**: `enterprise.db`
- **位置**: 项目根目录
- **类型**: SQLite 3.x

### 数据表结构

#### 1. 企业信息表 (enterprises)
```sql
CREATE TABLE enterprises (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,           -- 企业全称
    short_name TEXT,              -- 企业简称
    area TEXT,                    -- 所在区域
    location TEXT,                -- 具体位置
    contact_person TEXT,          -- 联系人
    contact_phone TEXT,           -- 联系电话
    risk_level TEXT,              -- 风险等级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 2. 报警记录表 (alarm_records)
```sql
CREATE TABLE alarm_records (
    id INTEGER PRIMARY KEY,
    enterprise_name TEXT NOT NULL,  -- 企业名称
    alarm_content TEXT,             -- 报警内容
    alarm_area TEXT,                -- 报警区域
    alarm_time TIMESTAMP,           -- 报警时间
    recovery_time TIMESTAMP,        -- 恢复时间
    is_valid BOOLEAN,               -- 是否有效
    alarm_type TEXT,                -- 报警类型
    alarm_status TEXT,              -- 报警状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 3. 风险等级表 (risk_levels)
```sql
CREATE TABLE risk_levels (
    id INTEGER PRIMARY KEY,
    enterprise_name TEXT NOT NULL,     -- 企业名称
    control_object TEXT,               -- 管控对象
    risk_analysis_object_code TEXT,    -- 风险分析对象代码
    risk_level TEXT,                   -- 风险等级
    responsible_department TEXT,       -- 责任部门
    responsible_person TEXT,           -- 责任人
    is_high_risk BOOLEAN,              -- 是否高风险
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 4. 作业活动表 (work_activities)
```sql
CREATE TABLE work_activities (
    id INTEGER PRIMARY KEY,
    plan_number TEXT,              -- 计划编号
    enterprise_name TEXT NOT NULL, -- 企业名称
    activity_content TEXT,         -- 活动内容
    work_area TEXT,                -- 作业区域
    workers_count INTEGER,         -- 作业人数
    work_type TEXT,                -- 作业类型
    risk_level TEXT,               -- 风险等级
    data_source TEXT,              -- 数据来源
    apply_time TIMESTAMP,          -- 申请时间
    planned_period TEXT,           -- 计划周期
    worker_category TEXT,          -- 作业人员类别
    formula TEXT,                  -- 公式
    remark TEXT,                   -- 备注
    is_high_risk BOOLEAN,          -- 是否高风险
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 5. 重大危险源表 (major_hazards)
```sql
CREATE TABLE major_hazards (
    id INTEGER PRIMARY KEY,
    enterprise_name TEXT NOT NULL,    -- 企业名称
    risk_source_name TEXT,            -- 危险源名称
    risk_source_short_name TEXT,      -- 危险源简称
    risk_source_code TEXT,            -- 危险源代码
    risk_source_level TEXT,           -- 危险源等级
    area TEXT,                        -- 区域
    location_type TEXT,               -- 位置类型
    chemicals_involved TEXT,          -- 涉及化学品(JSON格式)
    r_value REAL,                     -- R值
    is_high_level BOOLEAN,            -- 是否高等级
    record_date DATE,                 -- 记录日期
    valid_period TEXT,                -- 有效期
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

#### 6. 开停车记录表 (startup_shutdown_logs)
```sql
CREATE TABLE startup_shutdown_logs (
    id INTEGER PRIMARY KEY,
    enterprise_name TEXT NOT NULL,      -- 企业名称
    responsible_person TEXT,            -- 责任人
    contact TEXT,                       -- 联系方式
    operation_type TEXT,                -- 操作类型
    current_status TEXT,                -- 当前状态
    is_interlock_removed BOOLEAN,       -- 是否解除联锁
    start_time TIMESTAMP,               -- 开始时间
    planned_completion_time TIMESTAMP,  -- 计划完成时间
    facility_name TEXT,                 -- 设施名称
    area_name TEXT,                     -- 区域名称
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## 🚀 快速开始

### 1. 初始化数据库
```bash
# 运行数据库初始化脚本
python init_database.py
```

### 2. 验证数据库
```bash
# 运行数据库测试
python test_database.py
```

### 3. 演示集成功能
```bash
# 运行数据库集成演示
python demo_database_integration.py
```

## 📊 数据统计

初始化完成后的数据统计：
- **企业信息**: 3 条记录
- **报警记录**: 10 条记录
- **风险等级**: 8 条记录
- **作业活动**: 19 条记录
- **重大危险源**: 3 条记录
- **开停车记录**: 8 条记录

## 🔧 MCP Resource集成

### 新增的数据库工具

#### 1. 通用数据库查询
```python
@mcp.tool()
async def query_local_database(db_path: str = "./enterprise.db", query: str = "SELECT * FROM enterprises") -> Dict[str, Any]:
    """查询本地SQLite数据库"""
```

#### 2. 企业信息查询
```python
@mcp.tool()
async def query_enterprise_info(enterprise_name: str = None) -> Dict[str, Any]:
    """查询企业基础信息"""
```

#### 3. 报警记录查询
```python
@mcp.tool()
async def query_alarm_records(enterprise_name: str = None, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
    """查询报警记录"""
```

#### 4. 风险评估查询
```python
@mcp.tool()
async def query_risk_assessment(enterprise_name: str = None, high_risk_only: bool = False) -> Dict[str, Any]:
    """查询风险评估信息"""
```

### 智能资源选择

系统会根据用户请求自动选择最合适的数据源：

1. **简单数据读取** → 优先使用文件资源（速度快）
2. **复杂查询统计** → 自动选择数据库资源（功能强）
3. **文件不可用** → 自动fallback到数据库
4. **数据库不可用** → 自动fallback到文件

## 📈 数据库优势

### 1. 复杂查询能力
```sql
-- 企业风险综合评估
SELECT e.name, 
       COUNT(DISTINCT ar.id) as alarm_count,
       COUNT(DISTINCT rl.id) as risk_items,
       SUM(CASE WHEN rl.is_high_risk = 1 THEN 1 ELSE 0 END) as high_risk_count
FROM enterprises e
LEFT JOIN alarm_records ar ON e.name = ar.enterprise_name
LEFT JOIN risk_levels rl ON e.name = rl.enterprise_name
GROUP BY e.name
ORDER BY high_risk_count DESC;
```

### 2. 统计分析
```sql
-- 各企业报警统计
SELECT enterprise_name, 
       COUNT(*) as total_alarms,
       SUM(CASE WHEN is_valid = 1 THEN 1 ELSE 0 END) as valid_alarms,
       SUM(CASE WHEN alarm_type = '安全' THEN 1 ELSE 0 END) as safety_alarms
FROM alarm_records 
GROUP BY enterprise_name
ORDER BY total_alarms DESC;
```

### 3. 条件筛选
```sql
-- 高风险企业的最近报警
SELECT ar.enterprise_name, ar.alarm_area, ar.alarm_time
FROM alarm_records ar
JOIN risk_levels rl ON ar.enterprise_name = rl.enterprise_name
WHERE rl.is_high_risk = 1 
  AND ar.is_valid = 1
ORDER BY ar.alarm_time DESC;
```

## 🔄 智能Fallback机制

### 工作流程
1. **请求分析** - 分析用户请求，确定数据类别
2. **主要资源** - 尝试使用推荐的主要资源类型
3. **错误检测** - 检测主要资源是否可用
4. **自动切换** - 主要资源失败时自动切换到备用资源
5. **结果返回** - 返回统一格式的数据结果

### 示例场景
```
用户请求: "查看合全药业的报警记录"
↓
主要资源: file://./data/alarm_record.md
↓
文件不存在或读取失败
↓
自动fallback: database query
↓
执行: SELECT * FROM alarm_records WHERE enterprise_name LIKE '%合全%'
↓
返回数据库查询结果
```

## 🎯 使用建议

### 1. 数据源选择策略
- **文件资源**: 适用于简单的数据读取和展示
- **数据库资源**: 适用于复杂查询、统计分析、关联查询

### 2. 性能优化
- 为常用查询字段添加索引
- 使用连接池管理数据库连接
- 实现查询结果缓存

### 3. 数据维护
- 定期从markdown文件同步数据到数据库
- 设置数据备份和恢复机制
- 监控数据库性能和存储空间

## 🔒 安全考虑

1. **SQL注入防护** - 使用参数化查询
2. **访问权限控制** - 限制数据库文件访问权限
3. **数据验证** - 验证输入参数的合法性
4. **错误处理** - 避免敏感信息泄露

## 📝 维护指南

### 数据更新
```bash
# 重新初始化数据库（会清空现有数据）
python init_database.py

# 增量更新（需要自定义脚本）
python update_database.py
```

### 备份恢复
```bash
# 备份数据库
cp enterprise.db enterprise_backup_$(date +%Y%m%d).db

# 恢复数据库
cp enterprise_backup_20250130.db enterprise.db
```

### 性能监控
```sql
-- 查看表大小
SELECT name, COUNT(*) as row_count 
FROM sqlite_master sm
JOIN (SELECT 'enterprises' as name, COUNT(*) as cnt FROM enterprises
      UNION ALL SELECT 'alarm_records', COUNT(*) FROM alarm_records
      UNION ALL SELECT 'risk_levels', COUNT(*) FROM risk_levels) t
ON sm.name = t.name;
```

## ✅ 验证清单

- [x] 数据库文件创建成功
- [x] 所有表结构正确
- [x] 数据导入完整
- [x] 基础查询功能正常
- [x] 复杂查询功能正常
- [x] MCP工具集成成功
- [x] 智能路由功能正常
- [x] Fallback机制工作正常
- [x] 错误处理完善

数据库已准备就绪，可以用于生产环境的MCP Resource智能数据获取系统！
