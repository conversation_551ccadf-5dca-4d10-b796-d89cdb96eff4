# MCP Resource 正确实现指南

## 概述

这是对 fastmcp 的 `mcp_resource` 注解的正确实现，展示了 MCP Resource 的真正价值和用法。

## 🎯 MCP Resource 的核心价值

### 1. 声明式资源定义
通过 URI 模式声明资源，而不是定义工具函数：

```python
@mcp.resource("enterprise://info/{enterprise_name}")
async def get_enterprise_info(enterprise_name: str) -> str:
    """获取企业基础信息资源"""
```

### 2. 自动资源发现
MCP 客户端可以自动发现所有可用资源：

```python
# 客户端可以发现这些资源模式：
resources = [
    "enterprise://info/{enterprise_name}",
    "enterprise://alarms/{enterprise_name}",
    "enterprise://risks/{enterprise_name}",
    "data://file/{filename}",
    "db://query/{table}",
    "analytics://enterprise_summary",
    "analytics://risk_dashboard"
]
```

### 3. 语义化 URI
URI 本身就描述了资源的含义和用途：

- `enterprise://info/合全` - 合全药业的基础信息
- `enterprise://alarms/合全` - 合全药业的报警记录
- `enterprise://risks/天辰化工` - 天辰化工的风险评估
- `analytics://risk_dashboard` - 风险仪表板数据

### 4. 标准化访问协议
所有资源都通过统一的 MCP 协议访问，无需了解具体实现。

## 🔧 实现的资源类型

### 1. 企业信息资源
```python
@mcp.resource("enterprise://info/{enterprise_name}")
async def get_enterprise_info(enterprise_name: str) -> str:
    """获取企业基础信息"""

@mcp.resource("enterprise://alarms/{enterprise_name}")
async def get_enterprise_alarms(enterprise_name: str) -> str:
    """获取企业报警记录"""

@mcp.resource("enterprise://risks/{enterprise_name}")
async def get_enterprise_risks(enterprise_name: str) -> str:
    """获取企业风险评估"""
```

### 2. 数据文件资源
```python
@mcp.resource("data://file/{filename}")
async def get_data_file(filename: str) -> str:
    """获取数据文件内容"""
```

### 3. 数据库查询资源
```python
@mcp.resource("db://query/{table}")
async def query_table_resource(table: str) -> str:
    """查询数据库表"""
```

### 4. 分析统计资源
```python
@mcp.resource("analytics://enterprise_summary")
async def get_enterprise_summary() -> str:
    """获取企业综合分析"""

@mcp.resource("analytics://risk_dashboard")
async def get_risk_dashboard() -> str:
    """获取风险仪表板"""
```

## 🆚 MCP Resource vs MCP Tool

### MCP Tool (传统方式)
```python
@mcp.tool()
async def read_data_file(file_name: str) -> Dict[str, Any]:
    """读取数据文件工具"""
    # 面向功能的工具实现
```

**特点：**
- 面向功能，需要明确调用特定工具
- 客户端需要知道有哪些工具可用
- 工具名称和参数需要文档说明

### MCP Resource (新方式)
```python
@mcp.resource("data://file/{filename}")
async def get_data_file(filename: str) -> str:
    """获取数据文件资源"""
    # 面向数据的资源实现
```

**特点：**
- 面向数据，通过 URI 语义化访问资源
- 客户端可以自动发现和浏览资源
- URI 本身就是自文档化的

## 🤖 Agent 集成方式

### 1. 配置 MCP Resource 服务器
```python
# 在 agent.py 中配置
mcp_resource_server = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./resources.py"],
        timeout=120.0
    ),
    # 注意：MCP Resource 不需要 tool_filter
    # 资源通过 URI 模式自动发现和访问
)
```

### 2. Agent 智能路由
Agent 根据用户请求智能选择合适的资源：

```python
user_request = "查看合全药业的报警记录"
# Agent 分析后选择：enterprise://alarms/合全

user_request = "给我一个风险分析报告"
# Agent 分析后选择：analytics://risk_dashboard
```

## 🔍 资源发现和验证

### 1. 自动资源发现
```python
# MCP 客户端可以自动发现所有可用资源
available_resources = await mcp_client.list_resources()
```

### 2. 内置验证
- URI 模式验证
- 参数类型检查
- 安全性验证（防止路径遍历、SQL注入等）

### 3. 访问权限控制
可以在资源级别控制访问权限：

```python
@mcp.resource("enterprise://sensitive/{data}")
async def get_sensitive_data(data: str) -> str:
    # 可以在这里添加权限检查
    if not has_permission(data):
        return json.dumps({"error": "访问被拒绝"})
```

## 📊 使用场景对比

### 场景1：查询企业信息

**传统 Tool 方式：**
```python
# Agent 需要知道具体的工具名称
result = await call_tool("query_enterprise_info", {"enterprise_name": "合全"})
```

**MCP Resource 方式：**
```python
# Agent 通过语义化 URI 访问资源
result = await access_resource("enterprise://info/合全")
```

### 场景2：获取报警数据

**传统 Tool 方式：**
```python
# 需要选择是读文件还是查数据库
result1 = await call_tool("read_data_file", {"file_name": "alarm_record.md"})
result2 = await call_tool("query_database", {"query": "SELECT * FROM alarm_records"})
```

**MCP Resource 方式：**
```python
# 统一的资源访问方式
result = await access_resource("enterprise://alarms/合全")
```

## 🎯 核心优势

### 1. 语义化和自文档化
- URI 本身就描述了资源的含义
- 不需要额外的文档说明参数和用法

### 2. 自动发现
- 客户端可以自动发现所有可用资源
- 支持资源的动态浏览和探索

### 3. 标准化协议
- 统一的 MCP 协议访问所有资源
- 与具体实现解耦

### 4. 内置验证和安全
- 自动进行参数验证
- 内置安全检查机制

### 5. 可观测性
- 可以统计和监控资源使用情况
- 支持资源访问日志

### 6. 易于扩展
- 添加新资源只需要添加新的注解函数
- 不需要修改客户端代码

## 🚀 快速开始

### 1. 启动 MCP Resource 服务器
```bash
python resources.py
```

### 2. 测试资源访问
```bash
python demo_mcp_resource.py
```

### 3. 在 Agent 中使用
```python
# 配置 MCP Resource 服务器
mcp_resource_server = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./resources.py"],
        timeout=120.0
    )
)

# 在 Agent 中使用
root_agent = LlmAgent(
    tools=[mcp_resource_server],  # 添加 MCP Resource 服务器
    # ... 其他配置
)
```

## 📝 最佳实践

### 1. URI 设计原则
- 使用语义化的 scheme（如 `enterprise://`, `analytics://`）
- 保持 URI 结构的一致性
- 参数名称要清晰明确

### 2. 资源组织
- 按业务领域组织资源（企业、分析、数据等）
- 提供不同粒度的资源（详细信息、摘要、统计等）

### 3. 错误处理
- 统一的错误响应格式
- 提供有意义的错误信息

### 4. 性能优化
- 对频繁访问的资源进行缓存
- 实现资源的分页和限制

## ✅ 验证清单

- [x] 使用 `@mcp.resource()` 注解定义资源
- [x] 实现语义化的 URI 模式
- [x] 提供统一的 JSON 响应格式
- [x] 实现参数验证和安全检查
- [x] 支持自动资源发现
- [x] 集成到 Agent 工作流中
- [x] 提供完整的演示和文档

现在你有了真正的 MCP Resource 实现，体现了 fastmcp 的 `mcp_resource` 注解的核心价值！
