#!/usr/bin/env python3
"""
MCP Resource 服务器 - 真正的 mcp_resource 注解实现
使用 fastmcp 的 mcp_resource 注解定义资源，支持智能路由和验证
"""

import json
import os
import sqlite3
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse, parse_qs

from mcp.server.fastmcp import FastMCP

# 创建 FastMCP 服务器实例
mcp = FastMCP(name="ResourceServer")

# ==================== 企业数据资源 ====================

@mcp.resource("enterprise://info/{enterprise_name}")
async def get_enterprise_info(enterprise_name: str) -> str:
    """
    获取企业基础信息资源
    URI: enterprise://info/{enterprise_name}
    示例: enterprise://info/合全
    """
    try:
        # 从数据库查询企业信息
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "企业数据库不存在"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM enterprises
            WHERE name LIKE ? OR short_name LIKE ?
        """, (f"%{enterprise_name}%", f"%{enterprise_name}%"))

        rows = cursor.fetchall()
        enterprises = [dict(row) for row in rows]

        conn.close()

        return json.dumps({
            "resource_type": "enterprise_info",
            "enterprise_name": enterprise_name,
            "data": enterprises,
            "count": len(enterprises)
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"查询企业信息失败: {str(e)}"}, ensure_ascii=False)

@mcp.resource("enterprise://alarms/{enterprise_name}")
async def get_enterprise_alarms(enterprise_name: str) -> str:
    """
    获取企业报警记录资源
    URI: enterprise://alarms/{enterprise_name}
    示例: enterprise://alarms/合全
    """
    try:
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "企业数据库不存在"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM alarm_records
            WHERE enterprise_name LIKE ?
            ORDER BY alarm_time DESC
        """, (f"%{enterprise_name}%",))

        rows = cursor.fetchall()
        alarms = [dict(row) for row in rows]

        # 统计信息
        valid_alarms = [a for a in alarms if a.get('is_valid')]
        safety_alarms = [a for a in alarms if a.get('alarm_type') == '安全']

        conn.close()

        return json.dumps({
            "resource_type": "enterprise_alarms",
            "enterprise_name": enterprise_name,
            "data": alarms,
            "statistics": {
                "total_alarms": len(alarms),
                "valid_alarms": len(valid_alarms),
                "safety_alarms": len(safety_alarms)
            }
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"查询报警记录失败: {str(e)}"}, ensure_ascii=False)

@mcp.resource("enterprise://risks/{enterprise_name}")
async def get_enterprise_risks(enterprise_name: str) -> str:
    """
    获取企业风险评估资源
    URI: enterprise://risks/{enterprise_name}
    示例: enterprise://risks/天辰化工
    """
    try:
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "企业数据库不存在"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT * FROM risk_levels
            WHERE enterprise_name LIKE ?
            ORDER BY is_high_risk DESC, risk_level
        """, (f"%{enterprise_name}%",))

        rows = cursor.fetchall()
        risks = [dict(row) for row in rows]

        # 风险统计
        high_risks = [r for r in risks if r.get('is_high_risk')]
        risk_distribution = {}
        for risk in risks:
            level = risk.get('risk_level', '未知')
            risk_distribution[level] = risk_distribution.get(level, 0) + 1

        conn.close()

        return json.dumps({
            "resource_type": "enterprise_risks",
            "enterprise_name": enterprise_name,
            "data": risks,
            "statistics": {
                "total_risks": len(risks),
                "high_risks": len(high_risks),
                "risk_distribution": risk_distribution
            }
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"查询风险信息失败: {str(e)}"}, ensure_ascii=False)

# ==================== 数据文件资源 ====================

@mcp.resource("data://file/{filename}")
async def get_data_file(filename: str) -> str:
    """
    获取数据文件资源
    URI: data://file/{filename}
    示例: data://file/alarm_record.md
    """
    try:
        file_path = Path("./data") / filename
        if not file_path.exists():
            return json.dumps({"error": f"数据文件不存在: {filename}"}, ensure_ascii=False)

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 尝试提取JSON数据
        json_data = None
        if '[' in content and ']' in content:
            try:
                start_idx = content.find('[')
                end_idx = content.rfind(']') + 1
                json_str = content[start_idx:end_idx]
                json_data = json.loads(json_str)
            except:
                pass

        return json.dumps({
            "resource_type": "data_file",
            "filename": filename,
            "file_size": len(content),
            "has_json": json_data is not None,
            "json_records": len(json_data) if json_data else 0,
            "content": content if not json_data else None,
            "data": json_data
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"读取数据文件失败: {str(e)}"}, ensure_ascii=False)

# ==================== 数据库查询资源 ====================

@mcp.resource("db://query/{table}")
async def query_table_resource(table: str) -> str:
    """
    查询数据库表资源
    URI: db://query/{table}
    示例: db://query/enterprises
    """
    try:
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "数据库文件不存在"}, ensure_ascii=False)

        # 验证表名安全性
        valid_tables = ['enterprises', 'alarm_records', 'risk_levels',
                       'work_activities', 'major_hazards', 'startup_shutdown_logs']
        if table not in valid_tables:
            return json.dumps({"error": f"无效的表名: {table}"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute(f"SELECT * FROM {table} LIMIT 100")
        rows = cursor.fetchall()
        data = [dict(row) for row in rows]

        # 获取表结构信息
        cursor.execute(f"PRAGMA table_info({table})")
        columns = cursor.fetchall()
        schema = [{"name": col[1], "type": col[2], "not_null": bool(col[3])} for col in columns]

        conn.close()

        return json.dumps({
            "resource_type": "database_table",
            "table_name": table,
            "schema": schema,
            "data": data,
            "count": len(data)
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"查询数据库表失败: {str(e)}"}, ensure_ascii=False)

# ==================== 统计分析资源 ====================

@mcp.resource("analytics://enterprise_summary")
async def get_enterprise_summary() -> str:
    """
    获取企业综合分析资源
    URI: analytics://enterprise_summary
    """
    try:
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "数据库文件不存在"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 企业综合统计
        cursor.execute("""
            SELECT
                e.name as enterprise_name,
                e.short_name,
                COUNT(DISTINCT ar.id) as alarm_count,
                COUNT(DISTINCT rl.id) as risk_items,
                COUNT(DISTINCT wa.id) as work_activities,
                SUM(CASE WHEN ar.is_valid = 1 THEN 1 ELSE 0 END) as valid_alarms,
                SUM(CASE WHEN rl.is_high_risk = 1 THEN 1 ELSE 0 END) as high_risk_items
            FROM enterprises e
            LEFT JOIN alarm_records ar ON e.name = ar.enterprise_name
            LEFT JOIN risk_levels rl ON e.name = rl.enterprise_name
            LEFT JOIN work_activities wa ON e.name = wa.enterprise_name
            GROUP BY e.name, e.short_name
            ORDER BY valid_alarms DESC, high_risk_items DESC
        """)

        rows = cursor.fetchall()
        summary = [dict(row) for row in rows]

        conn.close()

        return json.dumps({
            "resource_type": "enterprise_summary",
            "data": summary,
            "generated_at": "2025-01-30T12:00:00Z"
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"生成企业摘要失败: {str(e)}"}, ensure_ascii=False)

@mcp.resource("analytics://risk_dashboard")
async def get_risk_dashboard() -> str:
    """
    获取风险仪表板资源
    URI: analytics://risk_dashboard
    """
    try:
        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return json.dumps({"error": "数据库文件不存在"}, ensure_ascii=False)

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 风险分布统计
        cursor.execute("""
            SELECT risk_level, COUNT(*) as count
            FROM risk_levels
            GROUP BY risk_level
            ORDER BY count DESC
        """)
        risk_distribution = [dict(row) for row in cursor.fetchall()]

        # 高风险企业
        cursor.execute("""
            SELECT enterprise_name, COUNT(*) as high_risk_count
            FROM risk_levels
            WHERE is_high_risk = 1
            GROUP BY enterprise_name
            ORDER BY high_risk_count DESC
        """)
        high_risk_enterprises = [dict(row) for row in cursor.fetchall()]

        # 最近报警统计
        cursor.execute("""
            SELECT enterprise_name, COUNT(*) as recent_alarms
            FROM alarm_records
            WHERE DATE(alarm_time) >= DATE('now', '-7 days')
            GROUP BY enterprise_name
            ORDER BY recent_alarms DESC
        """)
        recent_alarms = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return json.dumps({
            "resource_type": "risk_dashboard",
            "risk_distribution": risk_distribution,
            "high_risk_enterprises": high_risk_enterprises,
            "recent_alarms": recent_alarms,
            "generated_at": "2025-01-30T12:00:00Z"
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({"error": f"生成风险仪表板失败: {str(e)}"}, ensure_ascii=False)

if __name__ == "__main__":
    # 运行 MCP 服务器
    mcp.run()

