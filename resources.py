import requests
import sqlite3
from typing import Optional

from fastmcp.contrib.mcp_mixin import mcp_resource
from mcp.server.fastmcp import FastMCP

from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.agent_tool import AgentTool
# 创建 FastMCP 服务器实例
mcp = FastMCP(name="SpecialWorkTools1")



@mcp_resource("123")
def read_file(self, path: str) -> str:
    """读取文件内容"""
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"[ERROR] Failed to read file: {e}"

@mcp_resource("23")
def http_get(self, url: str, params: Optional[dict] = None) -> str:
    """发起 HTTP GET 请求"""
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.text
    except Exception as e:
        return f"[ERROR] Failed HTTP GET: {e}"

@mcp_resource("fetch_enterprise_fullname")
def query_sqlite(self, db_path: str, query: str) -> list[dict]:
    """执行 SQLite 查询"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute(query)
        rows = cur.fetchall()
        return [dict(row) for row in rows]
    except Exception as e:
        return [{"error": str(e)}]
    finally:
        conn.close()

from google.adk.tools.mcp_tool import MCPToolset

if __name__ == "__main__":

    fetch_enterprise_fullname_toolset = MCPToolset(
        tool_filter=['fetch_enterprise_fullname']
    )

    fetch_enterprise_fullname_toolset.run_server(StdioServerParameters())

