#!/usr/bin/env python3
"""
演示Agent使用MCP Resource的完整示例
展示如何根据不同的用户请求智能选择数据获取方式
"""

import json
from pathlib import Path
from resource_config import analyze_user_request, get_resource_instructions

def simulate_agent_decision(user_request: str):
    """
    模拟agent根据用户请求做出的决策过程
    """
    print(f"🤖 Agent收到用户请求: {user_request}")
    print("-" * 50)
    
    # 1. 分析用户请求
    analysis = analyze_user_request(user_request)
    
    print(f"📊 请求分析结果:")
    print(f"   推荐类别: {analysis['recommended_category'].value}")
    print(f"   置信度: {analysis['confidence_score']:.2f}")
    print(f"   主要资源类型: {analysis['primary_resource_type'].value}")
    print(f"   备用资源类型: {analysis.get('fallback_resource_type', 'None')}")
    
    # 2. 获取具体的执行指令
    instructions = get_resource_instructions(analysis['recommended_category'])
    print(f"\n🔧 执行指令:")
    print(instructions.strip())
    
    # 3. 模拟实际的资源获取
    if analysis['primary_resource_type'].value == "file":
        file_path = analysis.get('file_path')
        if file_path:
            result = simulate_file_access(file_path)
            print(f"\n📁 文件访问结果:")
            print(f"   {result}")
    
    print("\n" + "=" * 60)

def simulate_file_access(file_path: str):
    """模拟文件访问"""
    path = Path(file_path)
    if path.exists():
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试解析JSON数据
            if '[' in content and ']' in content:
                start_idx = content.find('[')
                end_idx = content.rfind(']') + 1
                json_str = content[start_idx:end_idx]
                try:
                    data = json.loads(json_str)
                    return f"✅ 成功读取文件，包含 {len(data)} 条JSON记录"
                except:
                    return f"✅ 成功读取文件，文本长度: {len(content)} 字符"
            else:
                return f"✅ 成功读取文件，文本长度: {len(content)} 字符"
        except Exception as e:
            return f"❌ 文件读取失败: {e}"
    else:
        return f"❌ 文件不存在: {file_path}"

def demo_scenarios():
    """演示不同场景下的agent决策"""
    
    scenarios = [
        "查看合全药业昨天的报警记录",
        "获取天辰化工PVC装置的风险等级",
        "查询2025年1月15日的特殊作业活动",
        "分析新阳科技的重大危险源信息",
        "查看氯乙烯转化单元的开停车记录",
        "获取当前的天气信息",
        "查询企业基础信息数据库",
        "读取系统配置文件"
    ]
    
    print("🎭 MCP Resource智能路由演示")
    print("=" * 60)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}:")
        simulate_agent_decision(scenario)

def demo_mcp_resource_patterns():
    """演示MCP Resource URI模式"""
    print("\n🔗 MCP Resource URI模式演示")
    print("=" * 60)
    
    resource_examples = [
        {
            "uri": "file://./data/alarm_record.md",
            "description": "读取本地报警记录文件",
            "usage": "当用户询问报警、传感器、监测相关信息时使用"
        },
        {
            "uri": "file://./data/risk_level.md", 
            "description": "读取风险等级数据文件",
            "usage": "当用户询问风险评估、安全等级相关信息时使用"
        },
        {
            "uri": "db://./enterprise.db?query=SELECT * FROM companies WHERE risk_level='高风险'",
            "description": "查询高风险企业数据库",
            "usage": "当需要复杂查询或统计分析时使用"
        },
        {
            "uri": "http://api.weather.com/current?location=常州",
            "description": "获取外部天气API数据",
            "usage": "当用户询问天气、环境条件等外部信息时使用"
        }
    ]
    
    for example in resource_examples:
        print(f"\n🔗 URI: {example['uri']}")
        print(f"📋 描述: {example['description']}")
        print(f"💡 使用场景: {example['usage']}")

def demo_agent_workflow():
    """演示完整的agent工作流程"""
    print("\n🔄 Agent工作流程演示")
    print("=" * 60)
    
    workflow_steps = [
        {
            "step": 1,
            "title": "接收用户请求",
            "description": "Agent接收用户的自然语言请求",
            "example": "用户: '查看合全药业的报警记录'"
        },
        {
            "step": 2,
            "title": "请求分析与路由",
            "description": "分析关键词，确定数据类别和资源类型",
            "example": "识别关键词: ['报警', '合全'] → 报警记录类别 → 文件资源"
        },
        {
            "step": 3,
            "title": "选择合适的工具",
            "description": "根据分析结果选择MCP工具",
            "example": "选择 read_data_file 工具，参数: alarm_record.md"
        },
        {
            "step": 4,
            "title": "执行资源获取",
            "description": "调用MCP工具获取数据",
            "example": "读取文件 → 解析JSON → 返回结构化数据"
        },
        {
            "step": 5,
            "title": "处理和响应",
            "description": "处理获取的数据并生成用户友好的响应",
            "example": "筛选合全药业相关记录 → 格式化输出 → 返回给用户"
        }
    ]
    
    for step in workflow_steps:
        print(f"\n📍 步骤 {step['step']}: {step['title']}")
        print(f"   📝 {step['description']}")
        print(f"   💡 示例: {step['example']}")

def main():
    """主演示函数"""
    print("🚀 MCP Resource智能代理演示")
    print("展示如何根据不同输出执行不同的数据获取")
    print("=" * 80)
    
    # 演示不同场景
    demo_scenarios()
    
    # 演示MCP Resource模式
    demo_mcp_resource_patterns()
    
    # 演示工作流程
    demo_agent_workflow()
    
    print("\n" + "=" * 80)
    print("✅ 演示完成")
    
    print("\n📋 总结:")
    print("1. ✅ 智能请求分析 - 根据关键词自动识别数据需求")
    print("2. ✅ 资源类型路由 - 自动选择文件/数据库/HTTP资源")
    print("3. ✅ MCP工具集成 - 无缝调用fastmcp工具")
    print("4. ✅ 错误处理机制 - 主要方式失败时自动fallback")
    print("5. ✅ 扩展性设计 - 易于添加新的资源类型和路由规则")
    
    print("\n🎯 实际部署建议:")
    print("- 将resource_tools.py作为独立的MCP服务器运行")
    print("- 在agent配置中添加MCPToolset指向该服务器")
    print("- 使用resource_config.py中的路由规则进行智能决策")
    print("- 根据实际业务需求调整关键词和置信度阈值")

if __name__ == "__main__":
    main()
