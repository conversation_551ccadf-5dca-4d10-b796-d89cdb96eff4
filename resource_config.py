#!/usr/bin/env python3
"""
资源配置文件
定义不同场景下的资源获取策略
"""

from typing import Dict, Any, List
from enum import Enum

class ResourceType(Enum):
    """资源类型枚举"""
    FILE = "file"
    DATABASE = "database" 
    HTTP = "http"
    HTTPS = "https"

class DataCategory(Enum):
    """数据类别枚举"""
    ENTERPRISE_INFO = "enterprise_info"      # 企业信息
    ALARM_RECORD = "alarm_record"           # 报警记录
    RISK_ASSESSMENT = "risk_assessment"     # 风险评估
    WORK_ACTIVITY = "work_activity"         # 作业活动
    EXTERNAL_API = "external_api"           # 外部API
    SYSTEM_CONFIG = "system_config"         # 系统配置

# 资源获取策略配置
RESOURCE_STRATEGIES = {
    DataCategory.ENTERPRISE_INFO: {
        "primary": ResourceType.FILE,
        "fallback": ResourceType.DATABASE,
        "file_path": "./data/area_info.md",
        "description": "企业基础信息优先从文件读取"
    },
    
    DataCategory.ALARM_RECORD: {
        "primary": ResourceType.FILE,
        "fallback": ResourceType.DATABASE,
        "file_path": "./data/alarm_record.md",
        "description": "报警记录数据从markdown文件读取"
    },
    
    DataCategory.RISK_ASSESSMENT: {
        "primary": ResourceType.FILE,
        "fallback": ResourceType.DATABASE,
        "file_path": "./data/risk_level.md",
        "description": "风险等级数据从文件读取"
    },
    
    DataCategory.WORK_ACTIVITY: {
        "primary": ResourceType.FILE,
        "fallback": ResourceType.DATABASE,
        "file_path": "./data/job_reporting_info.md",
        "description": "作业活动信息从文件读取"
    },
    
    DataCategory.EXTERNAL_API: {
        "primary": ResourceType.HTTP,
        "fallback": None,
        "description": "外部API数据通过HTTP获取"
    },
    
    DataCategory.SYSTEM_CONFIG: {
        "primary": ResourceType.FILE,
        "fallback": None,
        "file_path": "./config/system.json",
        "description": "系统配置从配置文件读取"
    }
}

# 智能路由规则 - 根据用户请求内容自动选择数据类别
ROUTING_RULES = [
    {
        "keywords": ["企业", "公司", "企业名", "全称", "简称"],
        "category": DataCategory.ENTERPRISE_INFO,
        "confidence": 0.9
    },
    {
        "keywords": ["报警", "警报", "传感器", "监测", "异常"],
        "category": DataCategory.ALARM_RECORD,
        "confidence": 0.9
    },
    {
        "keywords": ["风险", "危险", "安全", "风险等级", "评估"],
        "category": DataCategory.RISK_ASSESSMENT,
        "confidence": 0.8
    },
    {
        "keywords": ["作业", "活动", "工作", "计划", "申报"],
        "category": DataCategory.WORK_ACTIVITY,
        "confidence": 0.8
    },
    {
        "keywords": ["天气", "API", "外部", "网络", "在线"],
        "category": DataCategory.EXTERNAL_API,
        "confidence": 0.7
    },
    {
        "keywords": ["配置", "设置", "参数", "系统"],
        "category": DataCategory.SYSTEM_CONFIG,
        "confidence": 0.6
    }
]

def analyze_user_request(user_input: str) -> Dict[str, Any]:
    """
    分析用户请求，推荐合适的资源获取策略
    
    Args:
        user_input: 用户输入的请求内容
        
    Returns:
        包含推荐策略的字典
    """
    user_input_lower = user_input.lower()
    
    # 计算每个类别的匹配分数
    category_scores = {}
    
    for rule in ROUTING_RULES:
        category = rule["category"]
        keywords = rule["keywords"]
        confidence = rule["confidence"]
        
        # 计算关键词匹配数量
        matches = sum(1 for keyword in keywords if keyword in user_input_lower)
        if matches > 0:
            # 匹配分数 = 匹配关键词数 / 总关键词数 * 置信度
            score = (matches / len(keywords)) * confidence
            category_scores[category] = max(category_scores.get(category, 0), score)
    
    # 选择得分最高的类别
    if category_scores:
        best_category = max(category_scores.items(), key=lambda x: x[1])
        category, score = best_category
        
        strategy = RESOURCE_STRATEGIES.get(category, {})
        
        return {
            "recommended_category": category,
            "confidence_score": score,
            "resource_strategy": strategy,
            "primary_resource_type": strategy.get("primary"),
            "fallback_resource_type": strategy.get("fallback"),
            "file_path": strategy.get("file_path"),
            "description": strategy.get("description")
        }
    else:
        # 默认策略
        return {
            "recommended_category": DataCategory.ENTERPRISE_INFO,
            "confidence_score": 0.1,
            "resource_strategy": RESOURCE_STRATEGIES[DataCategory.ENTERPRISE_INFO],
            "primary_resource_type": ResourceType.FILE,
            "fallback_resource_type": ResourceType.DATABASE,
            "description": "未匹配到特定类别，使用默认策略"
        }

def get_resource_instructions(category: DataCategory) -> str:
    """
    获取特定数据类别的资源获取指令
    
    Args:
        category: 数据类别
        
    Returns:
        资源获取指令字符串
    """
    instructions = {
        DataCategory.ENTERPRISE_INFO: """
使用 read_data_file 工具读取企业信息：
- 文件路径: ./data/area_info.md
- 提取企业名称、区域信息等基础数据
- 如果文件读取失败，可尝试查询数据库
        """,
        
        DataCategory.ALARM_RECORD: """
使用 read_data_file 工具读取报警记录：
- 文件路径: ./data/alarm_record.md
- 解析JSON格式的报警数据
- 可按时间、区域、企业等条件筛选
        """,
        
        DataCategory.RISK_ASSESSMENT: """
使用 read_data_file 工具读取风险评估数据：
- 文件路径: ./data/risk_level.md
- 获取风险等级、控制对象等信息
- 支持按企业和区域查询
        """,
        
        DataCategory.WORK_ACTIVITY: """
使用 read_data_file 工具读取作业活动信息：
- 文件路径: ./data/job_reporting_info.md
- 获取作业计划、时间、地点等信息
- 可按日期和企业筛选
        """,
        
        DataCategory.EXTERNAL_API: """
使用 fetch_web_api 工具调用外部API：
- 支持GET/POST方法
- 自动处理JSON响应
- 设置合适的请求头和超时时间
        """,
        
        DataCategory.SYSTEM_CONFIG: """
使用 read_data_file 工具读取配置文件：
- 支持JSON、YAML等格式
- 获取系统参数和设置
- 确保配置文件存在且可读
        """
    }
    
    return instructions.get(category, "使用合适的资源获取工具")

# 示例使用场景
EXAMPLE_SCENARIOS = [
    {
        "user_request": "查看合全药业的报警记录",
        "expected_category": DataCategory.ALARM_RECORD,
        "expected_resource": ResourceType.FILE,
        "expected_file": "./data/alarm_record.md"
    },
    {
        "user_request": "获取天辰化工的风险等级",
        "expected_category": DataCategory.RISK_ASSESSMENT,
        "expected_resource": ResourceType.FILE,
        "expected_file": "./data/risk_level.md"
    },
    {
        "user_request": "查询今天的作业活动安排",
        "expected_category": DataCategory.WORK_ACTIVITY,
        "expected_resource": ResourceType.FILE,
        "expected_file": "./data/job_reporting_info.md"
    },
    {
        "user_request": "获取当前天气信息",
        "expected_category": DataCategory.EXTERNAL_API,
        "expected_resource": ResourceType.HTTP,
        "expected_file": None
    }
]

if __name__ == "__main__":
    # 测试资源路由功能
    print("🧪 测试资源路由功能")
    print("=" * 50)
    
    for scenario in EXAMPLE_SCENARIOS:
        user_request = scenario["user_request"]
        result = analyze_user_request(user_request)
        
        print(f"\n📝 用户请求: {user_request}")
        print(f"🎯 推荐类别: {result['recommended_category'].value}")
        print(f"📊 置信度: {result['confidence_score']:.2f}")
        print(f"🔧 主要资源类型: {result['primary_resource_type'].value}")
        print(f"📁 文件路径: {result.get('file_path', 'N/A')}")
        print(f"📋 说明: {result['description']}")
        
        # 验证预期结果
        expected_category = scenario["expected_category"]
        if result['recommended_category'] == expected_category:
            print("✅ 路由结果正确")
        else:
            print(f"❌ 路由结果错误，期望: {expected_category.value}")
    
    print("\n" + "=" * 50)
    print("✅ 资源路由测试完成")
