#!/usr/bin/env python3
"""
MCP Resource 真正用法演示
展示 fastmcp 的 mcp_resource 注解的核心价值和使用方式
"""

import asyncio
import json
from pathlib import Path

# 模拟 MCP 客户端访问资源的方式
class MCPResourceClient:
    """模拟MCP客户端，展示如何通过URI访问资源"""
    
    def __init__(self):
        self.available_resources = [
            # 企业信息资源
            "enterprise://info/{enterprise_name}",
            "enterprise://alarms/{enterprise_name}",
            "enterprise://risks/{enterprise_name}",
            
            # 数据文件资源
            "data://file/{filename}",
            
            # 数据库查询资源
            "db://query/{table}",
            
            # 分析统计资源
            "analytics://enterprise_summary",
            "analytics://risk_dashboard"
        ]
    
    async def discover_resources(self):
        """发现可用的MCP资源"""
        print("🔍 发现可用的MCP资源:")
        print("=" * 60)
        
        for i, resource_uri in enumerate(self.available_resources, 1):
            print(f"{i}. {resource_uri}")
        
        print(f"\n✅ 发现 {len(self.available_resources)} 个资源模式")
    
    async def access_resource(self, resource_uri: str, description: str = ""):
        """访问MCP资源"""
        print(f"\n🔗 访问资源: {resource_uri}")
        if description:
            print(f"📝 描述: {description}")
        
        # 这里模拟通过MCP协议访问资源
        # 实际使用时，这会通过MCP客户端调用对应的resource函数
        print(f"📡 通过MCP协议请求资源...")
        
        # 模拟资源访问结果
        if "enterprise://info/" in resource_uri:
            result = await self._simulate_enterprise_info(resource_uri)
        elif "enterprise://alarms/" in resource_uri:
            result = await self._simulate_enterprise_alarms(resource_uri)
        elif "enterprise://risks/" in resource_uri:
            result = await self._simulate_enterprise_risks(resource_uri)
        elif "data://file/" in resource_uri:
            result = await self._simulate_data_file(resource_uri)
        elif "db://query/" in resource_uri:
            result = await self._simulate_db_query(resource_uri)
        elif "analytics://" in resource_uri:
            result = await self._simulate_analytics(resource_uri)
        else:
            result = {"error": "未知的资源类型"}
        
        if "error" in result:
            print(f"❌ 资源访问失败: {result['error']}")
        else:
            print(f"✅ 资源访问成功")
            self._display_result(result)
        
        print("-" * 60)
    
    def _display_result(self, result):
        """显示资源访问结果"""
        if isinstance(result, dict):
            if "data" in result:
                data_count = len(result["data"]) if isinstance(result["data"], list) else 1
                print(f"📊 数据记录: {data_count} 条")
                
                if "statistics" in result:
                    print(f"📈 统计信息: {result['statistics']}")
                
                # 显示部分数据
                if isinstance(result["data"], list) and result["data"]:
                    print(f"📋 示例数据字段: {list(result['data'][0].keys())}")
                    if len(result["data"]) <= 3:
                        for i, item in enumerate(result["data"], 1):
                            print(f"   {i}. {item}")
                    else:
                        print(f"   显示前3条记录...")
                        for i, item in enumerate(result["data"][:3], 1):
                            print(f"   {i}. {item}")
            else:
                print(f"📄 结果: {result}")
    
    async def _simulate_enterprise_info(self, uri):
        """模拟企业信息资源访问"""
        return {
            "resource_type": "enterprise_info",
            "data": [
                {"name": "常州合全药业有限公司", "short_name": "合全", "risk_level": "中风险"}
            ],
            "count": 1
        }
    
    async def _simulate_enterprise_alarms(self, uri):
        """模拟企业报警资源访问"""
        return {
            "resource_type": "enterprise_alarms",
            "data": [
                {"id": 1, "alarm_content": "氯乙烯气体监测报警", "alarm_time": "2025-07-24 09:37:45"},
                {"id": 2, "alarm_content": "温度超限报警", "alarm_time": "2025-07-24 07:37:45"}
            ],
            "statistics": {
                "total_alarms": 8,
                "valid_alarms": 8,
                "safety_alarms": 8
            }
        }
    
    async def _simulate_enterprise_risks(self, uri):
        """模拟企业风险资源访问"""
        return {
            "resource_type": "enterprise_risks",
            "data": [
                {"control_object": "3#罐区", "risk_level": "重大风险", "is_high_risk": True}
            ],
            "statistics": {
                "total_risks": 1,
                "high_risks": 1,
                "risk_distribution": {"重大风险": 1}
            }
        }
    
    async def _simulate_data_file(self, uri):
        """模拟数据文件资源访问"""
        return {
            "resource_type": "data_file",
            "filename": "alarm_record.md",
            "has_json": True,
            "json_records": 10
        }
    
    async def _simulate_db_query(self, uri):
        """模拟数据库查询资源访问"""
        return {
            "resource_type": "database_table",
            "table_name": "enterprises",
            "data": [
                {"name": "常州合全药业有限公司", "short_name": "合全"},
                {"name": "华兴化工有限公司", "short_name": "华兴"}
            ],
            "count": 2
        }
    
    async def _simulate_analytics(self, uri):
        """模拟分析资源访问"""
        if "enterprise_summary" in uri:
            return {
                "resource_type": "enterprise_summary",
                "data": [
                    {"enterprise_name": "常州合全药业有限公司", "alarm_count": 8, "high_risk_items": 1}
                ]
            }
        else:
            return {
                "resource_type": "risk_dashboard",
                "risk_distribution": [{"risk_level": "重大风险", "count": 5}],
                "high_risk_enterprises": [{"enterprise_name": "常州合全药业有限公司", "high_risk_count": 1}]
            }

async def demo_mcp_resource_discovery():
    """演示MCP资源发现"""
    print("🎯 MCP Resource 核心价值演示")
    print("展示 fastmcp 的 mcp_resource 注解的真正作用")
    print("=" * 80)
    
    client = MCPResourceClient()
    await client.discover_resources()
    
    print("\n💡 MCP Resource 的核心价值:")
    print("1. 📋 声明式资源定义 - 通过URI模式声明资源")
    print("2. 🔍 自动资源发现 - MCP客户端可以自动发现可用资源")
    print("3. 🔗 标准化访问 - 通过统一的resource协议访问")
    print("4. 🎯 语义化URI - URI本身就描述了资源的含义和用途")

async def demo_resource_access_patterns():
    """演示不同的资源访问模式"""
    print("\n🔗 MCP Resource 访问模式演示")
    print("=" * 80)
    
    client = MCPResourceClient()
    
    # 企业信息资源
    await client.access_resource(
        "enterprise://info/合全",
        "获取合全药业的基础信息"
    )
    
    # 企业报警资源
    await client.access_resource(
        "enterprise://alarms/合全",
        "获取合全药业的报警记录"
    )
    
    # 企业风险资源
    await client.access_resource(
        "enterprise://risks/合全",
        "获取合全药业的风险评估信息"
    )
    
    # 数据文件资源
    await client.access_resource(
        "data://file/alarm_record.md",
        "获取报警记录数据文件"
    )
    
    # 数据库查询资源
    await client.access_resource(
        "db://query/enterprises",
        "查询企业数据库表"
    )
    
    # 分析统计资源
    await client.access_resource(
        "analytics://enterprise_summary",
        "获取企业综合分析报告"
    )
    
    await client.access_resource(
        "analytics://risk_dashboard",
        "获取风险仪表板数据"
    )

async def demo_agent_integration():
    """演示Agent如何使用MCP Resource"""
    print("\n🤖 Agent 集成 MCP Resource 演示")
    print("=" * 80)
    
    scenarios = [
        {
            "user_request": "查看合全药业的基本信息",
            "resource_uri": "enterprise://info/合全",
            "reasoning": "用户询问企业基本信息，直接使用企业信息资源"
        },
        {
            "user_request": "合全药业最近有什么报警吗？",
            "resource_uri": "enterprise://alarms/合全",
            "reasoning": "用户询问报警情况，使用企业报警资源"
        },
        {
            "user_request": "分析一下合全药业的风险状况",
            "resource_uri": "enterprise://risks/合全",
            "reasoning": "用户需要风险分析，使用企业风险资源"
        },
        {
            "user_request": "给我一个企业的综合报告",
            "resource_uri": "analytics://enterprise_summary",
            "reasoning": "用户需要综合分析，使用分析统计资源"
        },
        {
            "user_request": "当前的风险态势如何？",
            "resource_uri": "analytics://risk_dashboard",
            "reasoning": "用户询问整体风险态势，使用风险仪表板资源"
        }
    ]
    
    client = MCPResourceClient()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}:")
        print(f"👤 用户请求: {scenario['user_request']}")
        print(f"🧠 Agent推理: {scenario['reasoning']}")
        print(f"🎯 选择资源: {scenario['resource_uri']}")
        
        await client.access_resource(scenario['resource_uri'])

async def demo_resource_validation():
    """演示MCP Resource的验证功能"""
    print("\n✅ MCP Resource 验证功能演示")
    print("=" * 80)
    
    print("💡 MCP Resource 的验证优势:")
    print("1. 🔒 URI模式验证 - 确保资源URI格式正确")
    print("2. 📝 参数类型检查 - 自动验证参数类型")
    print("3. 🛡️ 访问权限控制 - 可以在资源级别控制访问")
    print("4. 📊 资源使用统计 - 可以统计资源的使用情况")
    
    print("\n🔍 验证示例:")
    
    validation_cases = [
        {
            "uri": "enterprise://info/合全",
            "status": "✅ 有效",
            "reason": "URI格式正确，企业名称参数有效"
        },
        {
            "uri": "enterprise://invalid/test",
            "status": "❌ 无效",
            "reason": "不支持的资源类型 'invalid'"
        },
        {
            "uri": "data://file/../../../etc/passwd",
            "status": "❌ 无效",
            "reason": "路径遍历攻击，安全验证失败"
        },
        {
            "uri": "db://query/users; DROP TABLE users;",
            "status": "❌ 无效",
            "reason": "SQL注入攻击，安全验证失败"
        }
    ]
    
    for case in validation_cases:
        print(f"🔗 {case['uri']}")
        print(f"   {case['status']} - {case['reason']}")

async def main():
    """主演示函数"""
    print("🚀 MCP Resource 真正用法演示")
    print("fastmcp 的 mcp_resource 注解核心价值")
    print("=" * 80)
    
    # 资源发现演示
    await demo_mcp_resource_discovery()
    
    # 资源访问模式演示
    await demo_resource_access_patterns()
    
    # Agent集成演示
    await demo_agent_integration()
    
    # 资源验证演示
    await demo_resource_validation()
    
    print("\n" + "=" * 80)
    print("✅ MCP Resource 演示完成")
    
    print("\n📋 MCP Resource 的核心优势总结:")
    print("1. 🎯 语义化URI - 资源URI本身就是自文档化的")
    print("2. 🔍 自动发现 - 客户端可以自动发现所有可用资源")
    print("3. 🔗 标准化协议 - 统一的MCP协议访问所有资源")
    print("4. 🛡️ 内置验证 - 自动进行参数验证和安全检查")
    print("5. 📊 可观测性 - 可以统计和监控资源使用情况")
    print("6. 🔧 易于扩展 - 添加新资源只需要添加新的注解函数")
    
    print("\n🎯 与传统tool的区别:")
    print("- Tool: 面向功能，需要明确调用特定工具")
    print("- Resource: 面向数据，通过URI语义化访问资源")
    print("- Tool: 客户端需要知道有哪些工具可用")
    print("- Resource: 客户端可以自动发现和浏览资源")

if __name__ == "__main__":
    asyncio.run(main())
