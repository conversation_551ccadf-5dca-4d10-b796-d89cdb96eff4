#!/usr/bin/env python3
"""
数据库集成演示
展示MCP Resource如何智能选择文件或数据库资源
"""

import asyncio
import json
from pathlib import Path
from resource_config import analyze_user_request

# 模拟MCP工具函数（实际使用时会通过MCP调用）
async def simulate_read_data_file(file_name: str):
    """模拟文件读取"""
    file_path = Path("./data") / file_name
    if not file_path.exists():
        return {"error": f"文件不存在: {file_name}"}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取JSON数据
        if '[' in content and ']' in content:
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            data = json.loads(json_str)
            return {
                "source": "file",
                "file_name": file_name,
                "data": data,
                "count": len(data)
            }
        else:
            return {
                "source": "file",
                "file_name": file_name,
                "content": content,
                "size": len(content)
            }
    except Exception as e:
        return {"error": f"文件读取失败: {str(e)}"}

async def simulate_query_database(query: str, db_path: str = "./enterprise.db"):
    """模拟数据库查询"""
    try:
        import sqlite3
        
        if not Path(db_path).exists():
            return {"error": f"数据库文件不存在: {db_path}"}
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        result = {
            "source": "database",
            "query": query,
            "data": [dict(row) for row in rows],
            "count": len(rows)
        }
        
        conn.close()
        return result
        
    except Exception as e:
        return {"error": f"数据库查询失败: {str(e)}"}

async def smart_data_retrieval(user_request: str):
    """智能数据获取演示"""
    print(f"🤖 用户请求: {user_request}")
    print("-" * 60)
    
    # 1. 分析用户请求
    analysis = analyze_user_request(user_request)
    
    print(f"📊 请求分析:")
    print(f"   类别: {analysis['recommended_category'].value}")
    print(f"   置信度: {analysis['confidence_score']:.2f}")
    print(f"   主要资源: {analysis['primary_resource_type'].value}")
    print(f"   备用资源: {analysis.get('fallback_resource_type', 'None')}")
    
    # 2. 尝试主要资源获取方式
    primary_result = None
    if analysis['primary_resource_type'].value == "file":
        file_path = analysis.get('file_path')
        if file_path:
            file_name = Path(file_path).name
            print(f"\n🔍 尝试文件资源: {file_name}")
            primary_result = await simulate_read_data_file(file_name)
    
    # 3. 如果主要方式失败，尝试备用方式
    fallback_result = None
    if primary_result and "error" in primary_result:
        fallback_type = analysis.get('fallback_resource_type')
        if fallback_type and fallback_type.value == "database":
            print(f"⚠️  文件读取失败，尝试数据库查询...")
            
            # 根据类别选择合适的数据库查询
            category = analysis['recommended_category'].value
            if category == "alarm_record":
                query = "SELECT * FROM alarm_records ORDER BY alarm_time DESC LIMIT 10"
            elif category == "risk_assessment":
                query = "SELECT * FROM risk_levels ORDER BY is_high_risk DESC"
            elif category == "work_activity":
                query = "SELECT * FROM work_activities ORDER BY apply_time DESC LIMIT 10"
            elif category == "enterprise_info":
                query = "SELECT * FROM enterprises"
            else:
                query = "SELECT name FROM sqlite_master WHERE type='table'"
            
            print(f"🔍 执行数据库查询: {query}")
            fallback_result = await simulate_query_database(query)
    
    # 4. 显示结果
    final_result = primary_result if primary_result and "error" not in primary_result else fallback_result
    
    if final_result:
        if "error" in final_result:
            print(f"❌ 获取失败: {final_result['error']}")
        else:
            print(f"\n✅ 数据获取成功:")
            print(f"   数据源: {final_result['source']}")
            print(f"   记录数: {final_result['count']}")
            
            # 显示部分数据
            if 'data' in final_result and final_result['data']:
                print(f"   示例数据: {list(final_result['data'][0].keys())}")
                
                # 根据数据类型显示相关信息
                if analysis['recommended_category'].value == "alarm_record":
                    alarms = final_result['data']
                    valid_alarms = [a for a in alarms if a.get('is_valid')]
                    print(f"   有效报警: {len(valid_alarms)} 条")
                    
                elif analysis['recommended_category'].value == "enterprise_info":
                    enterprises = final_result['data']
                    print(f"   企业列表: {[e.get('name', e.get('short_name', '未知')) for e in enterprises[:3]]}")
                    
                elif analysis['recommended_category'].value == "risk_assessment":
                    risks = final_result['data']
                    high_risks = [r for r in risks if r.get('is_high_risk')]
                    print(f"   高风险项: {len(high_risks)} 个")
    
    print("\n" + "=" * 80)

async def demo_scenarios():
    """演示不同场景"""
    scenarios = [
        "查看合全药业的报警记录",
        "获取所有企业的基础信息", 
        "查询高风险项目列表",
        "分析天辰化工的风险等级",
        "查看最近的作业活动",
        "统计各企业的报警数量",
        "查询正在进行的高风险作业",
        "获取重大危险源信息"
    ]
    
    print("🎭 智能数据获取演示")
    print("展示文件和数据库资源的智能选择")
    print("=" * 80)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}:")
        await smart_data_retrieval(scenario)

async def demo_database_advantages():
    """演示数据库相对于文件的优势"""
    print("\n🔍 数据库查询优势演示")
    print("=" * 80)
    
    database_queries = [
        {
            "description": "复杂统计查询 - 各企业报警统计",
            "query": """
                SELECT enterprise_name, 
                       COUNT(*) as total_alarms,
                       SUM(CASE WHEN is_valid = 1 THEN 1 ELSE 0 END) as valid_alarms,
                       SUM(CASE WHEN alarm_type = '安全' THEN 1 ELSE 0 END) as safety_alarms
                FROM alarm_records 
                GROUP BY enterprise_name
                ORDER BY total_alarms DESC
            """
        },
        {
            "description": "关联查询 - 企业风险综合评估",
            "query": """
                SELECT e.name, e.short_name,
                       COUNT(DISTINCT ar.id) as alarm_count,
                       COUNT(DISTINCT rl.id) as risk_items,
                       SUM(CASE WHEN rl.is_high_risk = 1 THEN 1 ELSE 0 END) as high_risk_count
                FROM enterprises e
                LEFT JOIN alarm_records ar ON e.name = ar.enterprise_name
                LEFT JOIN risk_levels rl ON e.name = rl.enterprise_name
                GROUP BY e.name, e.short_name
                ORDER BY high_risk_count DESC, alarm_count DESC
            """
        },
        {
            "description": "条件筛选 - 最近24小时的安全报警",
            "query": """
                SELECT enterprise_name, alarm_area, alarm_content, alarm_time
                FROM alarm_records 
                WHERE alarm_type = '安全' 
                  AND is_valid = 1
                  AND DATE(alarm_time) >= DATE('now', '-1 day')
                ORDER BY alarm_time DESC
            """
        }
    ]
    
    for i, query_info in enumerate(database_queries, 1):
        print(f"\n📊 查询 {i}: {query_info['description']}")
        print(f"🔍 SQL: {query_info['query'].strip()}")
        
        result = await simulate_query_database(query_info['query'])
        
        if result and "error" not in result:
            print(f"✅ 查询成功: {result['count']} 条记录")
            if result['data']:
                print(f"📋 字段: {list(result['data'][0].keys())}")
                # 显示前2条记录
                for j, record in enumerate(result['data'][:2]):
                    print(f"   {j+1}. {record}")
        else:
            print(f"❌ 查询失败: {result.get('error', '未知错误')}")
        
        print("-" * 60)

async def demo_intelligent_fallback():
    """演示智能fallback机制"""
    print("\n🔄 智能Fallback机制演示")
    print("=" * 80)
    
    # 模拟文件不存在的情况
    print("📋 场景: 文件资源不可用时的自动fallback")
    
    # 临时移动文件来模拟文件不存在
    alarm_file = Path("./data/alarm_record.md")
    backup_file = Path("./data/alarm_record.md.backup")
    
    file_exists = alarm_file.exists()
    if file_exists:
        alarm_file.rename(backup_file)
        print("🔧 临时移除alarm_record.md文件")
    
    try:
        # 尝试查询报警记录
        await smart_data_retrieval("查看合全药业的报警记录")
        
    finally:
        # 恢复文件
        if file_exists and backup_file.exists():
            backup_file.rename(alarm_file)
            print("🔧 恢复alarm_record.md文件")

async def main():
    """主演示函数"""
    print("🚀 MCP Resource数据库集成演示")
    print("展示智能数据获取和fallback机制")
    print("=" * 80)
    
    # 演示不同场景
    await demo_scenarios()
    
    # 演示数据库查询优势
    await demo_database_advantages()
    
    # 演示智能fallback
    await demo_intelligent_fallback()
    
    print("\n" + "=" * 80)
    print("✅ 演示完成")
    
    print("\n📋 总结:")
    print("1. ✅ 智能资源选择 - 根据请求类型自动选择文件或数据库")
    print("2. ✅ 自动fallback - 主要方式失败时自动尝试备用方案")
    print("3. ✅ 数据库优势 - 支持复杂查询、统计分析、关联查询")
    print("4. ✅ 统一接口 - 无论数据源如何，提供一致的访问体验")
    print("5. ✅ 错误处理 - 完善的异常处理和用户友好的错误信息")
    
    print("\n🎯 实际应用建议:")
    print("- 简单数据读取优先使用文件资源（速度快）")
    print("- 复杂查询和统计分析使用数据库资源（功能强）")
    print("- 配置智能路由规则，让agent自动选择最佳方案")
    print("- 设置合理的fallback策略，提高系统可靠性")

if __name__ == "__main__":
    asyncio.run(main())
