#!/usr/bin/env python3
"""
MCP Resource Tools
提供基于MCP resource的工具函数，用于agent调用
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from mcp.server.fastmcp import FastMCP

# 创建 FastMCP 服务器实例
mcp = FastMCP(name="ResourceTools")

@mcp.tool()
async def read_data_file(file_name: str, data_dir: str = "./data") -> Dict[str, Any]:
    """
    读取data目录下的数据文件
    :param file_name: 文件名（如 alarm_record.md）
    :param data_dir: 数据目录路径
    :return: 文件内容和元信息
    """
    try:
        file_path = Path(data_dir) / file_name
        if not file_path.exists():
            return {"error": f"文件不存在: {file_path}"}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果是markdown文件，尝试提取JSON数据
        if file_path.suffix.lower() == '.md':
            json_data = extract_json_from_markdown(content)
            return {
                "file_name": file_name,
                "file_type": "markdown",
                "raw_content": content,
                "json_data": json_data,
                "record_count": len(json_data) if json_data else 0
            }
        else:
            return {
                "file_name": file_name,
                "file_type": file_path.suffix,
                "content": content
            }
    except Exception as e:
        return {"error": f"读取文件失败: {str(e)}"}

@mcp.tool()
async def query_local_database(db_path: str = "./enterprise.db", query: str = "SELECT * FROM enterprises") -> Dict[str, Any]:
    """
    查询本地SQLite数据库
    :param db_path: 数据库文件路径（默认为./enterprise.db）
    :param query: SQL查询语句
    :return: 查询结果
    """
    try:
        import sqlite3

        if not os.path.exists(db_path):
            return {"error": f"数据库文件不存在: {db_path}"}

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute(query)
        rows = cursor.fetchall()

        result = {
            "database": db_path,
            "query": query,
            "rows": [dict(row) for row in rows],
            "count": len(rows)
        }

        conn.close()
        return result

    except Exception as e:
        return {"error": f"数据库查询失败: {str(e)}"}

@mcp.tool()
async def query_enterprise_info(enterprise_name: str = None) -> Dict[str, Any]:
    """
    查询企业基础信息
    :param enterprise_name: 企业名称（可选，为空时返回所有企业）
    :return: 企业信息
    """
    try:
        import sqlite3

        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return {"error": f"数据库文件不存在: {db_path}"}

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if enterprise_name:
            cursor.execute("""
                SELECT * FROM enterprises
                WHERE name LIKE ? OR short_name LIKE ?
            """, (f"%{enterprise_name}%", f"%{enterprise_name}%"))
        else:
            cursor.execute("SELECT * FROM enterprises")

        rows = cursor.fetchall()

        result = {
            "query_type": "enterprise_info",
            "enterprise_name": enterprise_name,
            "enterprises": [dict(row) for row in rows],
            "count": len(rows)
        }

        conn.close()
        return result

    except Exception as e:
        return {"error": f"企业信息查询失败: {str(e)}"}

@mcp.tool()
async def query_alarm_records(enterprise_name: str = None, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
    """
    查询报警记录
    :param enterprise_name: 企业名称（可选）
    :param start_date: 开始日期（可选，格式：YYYY-MM-DD）
    :param end_date: 结束日期（可选，格式：YYYY-MM-DD）
    :return: 报警记录
    """
    try:
        import sqlite3

        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return {"error": f"数据库文件不存在: {db_path}"}

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 构建查询条件
        conditions = []
        params = []

        if enterprise_name:
            conditions.append("enterprise_name LIKE ?")
            params.append(f"%{enterprise_name}%")

        if start_date:
            conditions.append("DATE(alarm_time) >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("DATE(alarm_time) <= ?")
            params.append(end_date)

        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

        query = f"""
            SELECT * FROM alarm_records
            {where_clause}
            ORDER BY alarm_time DESC
        """

        cursor.execute(query, params)
        rows = cursor.fetchall()

        result = {
            "query_type": "alarm_records",
            "enterprise_name": enterprise_name,
            "date_range": f"{start_date or 'all'} to {end_date or 'all'}",
            "alarms": [dict(row) for row in rows],
            "count": len(rows)
        }

        conn.close()
        return result

    except Exception as e:
        return {"error": f"报警记录查询失败: {str(e)}"}

@mcp.tool()
async def query_risk_assessment(enterprise_name: str = None, high_risk_only: bool = False) -> Dict[str, Any]:
    """
    查询风险评估信息
    :param enterprise_name: 企业名称（可选）
    :param high_risk_only: 是否只查询高风险项目（默认False）
    :return: 风险评估信息
    """
    try:
        import sqlite3

        db_path = "./enterprise.db"
        if not os.path.exists(db_path):
            return {"error": f"数据库文件不存在: {db_path}"}

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 构建查询条件
        conditions = []
        params = []

        if enterprise_name:
            conditions.append("enterprise_name LIKE ?")
            params.append(f"%{enterprise_name}%")

        if high_risk_only:
            conditions.append("is_high_risk = 1")

        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

        query = f"""
            SELECT * FROM risk_levels
            {where_clause}
            ORDER BY is_high_risk DESC, risk_level
        """

        cursor.execute(query, params)
        rows = cursor.fetchall()

        result = {
            "query_type": "risk_assessment",
            "enterprise_name": enterprise_name,
            "high_risk_only": high_risk_only,
            "risk_items": [dict(row) for row in rows],
            "count": len(rows)
        }

        conn.close()
        return result

    except Exception as e:
        return {"error": f"风险评估查询失败: {str(e)}"}

@mcp.tool()
async def fetch_web_api(url: str, method: str = "GET", 
                       params: Optional[Dict] = None,
                       headers: Optional[Dict] = None) -> Dict[str, Any]:
    """
    调用Web API获取数据
    :param url: API地址
    :param method: HTTP方法（GET/POST）
    :param params: 请求参数
    :param headers: 请求头
    :return: API响应结果
    """
    try:
        import requests
        
        if headers is None:
            headers = {
                "User-Agent": "MCP-Resource-Tools/1.0",
                "Accept": "application/json"
            }
        
        if method.upper() == "GET":
            response = requests.get(url, params=params, headers=headers, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=params, headers=headers, timeout=30)
        else:
            return {"error": f"不支持的HTTP方法: {method}"}
        
        response.raise_for_status()
        
        # 尝试解析JSON
        try:
            content = response.json()
            content_type = "json"
        except:
            content = response.text
            content_type = "text"
        
        return {
            "url": url,
            "method": method,
            "status_code": response.status_code,
            "content_type": content_type,
            "content": content,
            "success": True
        }
        
    except Exception as e:
        return {"error": f"API请求失败: {str(e)}", "success": False}

@mcp.tool()
async def get_resource_info(resource_type: str, resource_path: str) -> Dict[str, Any]:
    """
    获取资源信息和元数据
    :param resource_type: 资源类型（file/database/http）
    :param resource_path: 资源路径
    :return: 资源信息
    """
    try:
        if resource_type.lower() == "file":
            path = Path(resource_path)
            if not path.exists():
                return {"error": f"文件不存在: {resource_path}"}
            
            stat = path.stat()
            return {
                "resource_type": "file",
                "path": str(path.absolute()),
                "size": stat.st_size,
                "modified_time": stat.st_mtime,
                "is_readable": os.access(path, os.R_OK),
                "file_extension": path.suffix
            }
        
        elif resource_type.lower() == "database":
            if not os.path.exists(resource_path):
                return {"error": f"数据库文件不存在: {resource_path}"}
            
            import sqlite3
            conn = sqlite3.connect(resource_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            return {
                "resource_type": "database",
                "path": resource_path,
                "tables": tables,
                "table_count": len(tables)
            }
        
        elif resource_type.lower() == "http":
            import requests
            try:
                response = requests.head(resource_path, timeout=10)
                return {
                    "resource_type": "http",
                    "url": resource_path,
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "accessible": response.status_code == 200
                }
            except:
                return {
                    "resource_type": "http",
                    "url": resource_path,
                    "accessible": False,
                    "error": "无法访问该URL"
                }
        
        else:
            return {"error": f"不支持的资源类型: {resource_type}"}
    
    except Exception as e:
        return {"error": f"获取资源信息失败: {str(e)}"}

def extract_json_from_markdown(content: str) -> List[Dict]:
    """从markdown内容中提取JSON数据"""
    try:
        import json
        # 查找 JSON 数组的开始和结束
        start_idx = content.find('[')
        end_idx = content.rfind(']') + 1
        
        if start_idx == -1 or end_idx == 0:
            return []
        
        json_str = content[start_idx:end_idx]
        return json.loads(json_str)
    except:
        return []

if __name__ == "__main__":
    # 运行 MCP 服务器
    mcp.run()
