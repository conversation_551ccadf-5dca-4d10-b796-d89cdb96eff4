from datetime import datetime
from google.adk.agents import Agent, LlmAgent, ParallelAgent, SequentialAgent
from google.adk.tools.agent_tool import AgentTool
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters, SseConnectionParams
from google.adk.models.lite_llm import LiteLlm

from .sub_agents import sw_system_agent, video_system_agent

def get_current_time() -> dict:
    """Returns the current date / time.

    Returns:
        dict: status and result or error msg.
    """

    now = datetime.now()
    report = (
        f'The current time is {now.strftime("%Y-%m-%d %H:%M:%S %Z%z")}'
    )
    return {"status": "success", "report": report}


################ MCP Server #########################

# MCP Resource 服务器 - 真正的 mcp_resource 注解实现
mcp_resource_server = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./resources.py"],
        timeout=120.0
    ),
    # MCP Resource 不需要 tool_filter，因为它提供的是资源而不是工具
    # 资源通过 URI 模式自动发现和访问
)

fetch_enterprise_fullname_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_enterprise_fullname']
)

fetch_spwork_activities_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_spwork_activities']
)

fetch_spwork_area_risk_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_spwork_area_risk']
)

fetch_alarm_record_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_alarm_record']
)

fetch_major_hazard_sources_info_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_major_hazard_sources_info']
)

fetch_ent_area_startup_shutdown_log_toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command='python',
        args=["./spwork_mcpserver.py"],
        timeout=120.0
    ),
    tool_filter=['fetch_ent_area_startup_shutdown_log']
)

#####################
now = datetime.now()
current_date = now.strftime("%Y-%m-%d")
current_time = now.strftime("%H:%M:%S")

# MCP Resource 智能路由代理 - 使用真正的 mcp_resource 注解
mcp_resource_agent = LlmAgent(
    model=LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name='mcp_resource_agent',
    description='MCP Resource智能路由助手，通过语义化URI访问企业数据资源',
    instruction=f'''你是一个MCP Resource智能路由助手，能够根据用户请求自动选择合适的资源URI进行数据访问。

**可用的MCP资源URI模式：**

1. **企业相关资源：**
   - enterprise://info/{{企业名}} - 获取企业基础信息
   - enterprise://alarms/{{企业名}} - 获取企业报警记录
   - enterprise://risks/{{企业名}} - 获取企业风险评估

2. **数据文件资源：**
   - data://file/job_reporting_info.md - 作业申报信息文件（包含特殊作业数据）
   - data://file/alarm_record.md - 报警记录文件
   - data://file/risk_level.md - 风险等级文件
   - data://file/major_hazard_sources.md - 重大危险源文件
   - data://file/startup_shutdown_log.md - 开停车记录文件

3. **数据库查询资源：**
   - db://query/work_activities - 作业活动表（查询特殊作业）
   - db://query/enterprises - 企业表
   - db://query/alarm_records - 报警记录表
   - db://query/risk_levels - 风险等级表

4. **分析统计资源：**
   - analytics://enterprise_summary - 企业综合分析
   - analytics://risk_dashboard - 风险仪表板

**智能路由规则：**
- 用户询问特定企业信息 → enterprise://info/{{企业名}}
- 用户询问企业报警 → enterprise://alarms/{{企业名}}
- 用户询问企业风险 → enterprise://risks/{{企业名}}
- 用户询问特殊作业/作业活动 → data://file/job_reporting_info.md 或 db://query/work_activities
- 用户询问天气信息 → 使用外部API（需要实现HTTP资源）
- 用户询问综合分析 → analytics://enterprise_summary
- 用户询问风险态势 → analytics://risk_dashboard

**企业名称映射：**
- 天辰化工 → 天辰化工有限公司
- 合全药业 → 常州合全药业有限公司
- 新阳科技 → 新阳科技集团有限公司

**特殊处理：**
- 对于日期查询（如7月23日），优先使用数据库资源进行精确查询
- 对于外部信息（如天气），需要使用HTTP资源或提示用户该功能需要配置

当前时间: {current_date} {current_time}''',
    output_key='mcp_resource_result',
    tools=[mcp_resource_server]
)

fetch_spwork_area_risk_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name = 'fetch_spwork_area_risk_agent',
    description = '根据企业名字全称和区域名称，获取该区域的风险等级',
    instruction = '根据企业名字全称和区域名称，获取该区域的风险等级',
    output_key = 'fetch_spwork_area_risk_agent',
    tools = [fetch_spwork_area_risk_toolset]
)

fetch_spwork_area_alarm_risk_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name = 'fetch_spwork_area_alarm_risk_agent',
    description = '根据企业的传感器报警信息评估周边环境的风险等级',
    instruction = 'step 1. 根据企业名字全称、区域名称、时间来获取该区域的传感器报警信息\n step 2. 根据传感器报警记录评估风险等级',
    output_key = 'fetch_spwork_area_alarm_risk_agent',
    tools = [fetch_alarm_record_toolset]
)

fetch_spwork_area_whereis_majority_risk_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name = 'fetch_spwork_area_whereis_majority_risk_agent',
    description = '根据企业名字全称、区域名称、时间来获取该区域的传感器报警信息，根据区域的传感器报警信息评估周边环境的风险等级',
    instruction = '根据企业名字全称、区域名称、时间来获取该区域的传感器报警信息，根据区域的传感器报警信息评估周边环境的风险等级',
    output_key = 'fetch_spwork_area_whereis_majority_risk_agent',
    tools = [fetch_major_hazard_sources_info_toolset]
)

fetch_spwork_wheris_in_openparking_status_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name = 'fetch_spwork_wheris_in_openparking_status_agent',
    description = '根据企业名全称、区域名称、时间来获取该区域的开停车记录，根据开停车记录评估周边环境的风险等级',
    instruction = '首先获取该区域的开停车记录，如果处于开车状态，那么评估该区域的风险等级为高风险，否则为低风险',
    output_key = 'fetch_spwork_wheris_in_openparking_status_agent',
    tools = [fetch_ent_area_startup_shutdown_log_toolset]
)

parallel_analysis_spwork_risk_agent = ParallelAgent(
    name = 'ParallelRiskAnalysisAgent',
    description = '并行运行多个智能体获取活动的风险等级',
    sub_agents = [
        fetch_spwork_area_risk_agent,
        fetch_spwork_area_alarm_risk_agent,
        fetch_spwork_area_whereis_majority_risk_agent,
        fetch_spwork_wheris_in_openparking_status_agent]
)

analysis_summary_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen2.5-72b-instruct'),
    name = 'analysis_summary_agent',
    description = '对多个智能体的获取的风险等级结果进行归纳总结',
    instruction = "读取多个智能体的结果并进行总结，要求：只要有一个智能体的结果为'高风险',则判定最终结果为'高风险'"
)

sequential_spwork_risklevel_analysis_agent = SequentialAgent(
    name = 'sequential_spwork_risklevel_analysis_agent',
    sub_agents = [parallel_analysis_spwork_risk_agent, analysis_summary_agent],
    description = '根据作业活动的信息顺序执行子智能体获取活动的安全风险等级并归纳总结'
)
sequential_spwork_risklevel_analysis_tool = AgentTool(agent=sequential_spwork_risklevel_analysis_agent)

# MCP Resource 智能路由工具
mcp_resource_tool = AgentTool(agent=mcp_resource_agent)


root_agent_instruction = f"""你是一位智慧园区的助手，在化工园区的管理工作中经验丰富。你需要从用户的对话中分析出用户的真实意图，并调用合适的工具来完成用户的需求。

**核心能力：**
1. **特殊作业管理**：查询企业信息、作业活动、风险评估等
2. **MCP资源访问**：通过语义化URI智能访问企业数据资源
3. **风险分析评估**：综合多维度数据进行安全风险评估

**工作流程：**
step1、分析用户问题，识别数据需求类型（企业信息、作业数据、风险评估、资源获取等）
step2、根据需求类型选择合适的工具：
  - 企业基础信息查询：优先使用 mcp_resource_tool
  - 特殊作业查询：使用 mcp_resource_tool 或传统工具集
  - 风险评估：使用风险分析工具
  - 外部信息（如天气）：提示用户该功能需要配置
step3、整合多个工具的结果，提供综合性的回答

**MCP Resource使用示例：**
- 查询天辰化工信息 → 使用 mcp_resource_tool
- 查询7月23日特殊作业 → 使用 mcp_resource_tool
- 获取企业风险评估 → 使用 mcp_resource_tool
- 获取天气信息 → 提示需要配置外部API

当前日期:{current_date} 当前时间:{current_time}"""

root_agent = LlmAgent(
    model = LiteLlm(model='openai/qwen-plus'),
    name = 'Coordinator',
    description = '智慧园区综合助手，具备特殊作业管理和MCP资源智能访问能力',
    tools = [
        # MCP Resource 智能路由工具（优先使用）
        mcp_resource_tool,
        # 特殊作业相关工具（备用）
        fetch_enterprise_fullname_toolset,
        fetch_spwork_activities_toolset,
        sequential_spwork_risklevel_analysis_tool,
        fetch_ent_area_startup_shutdown_log_toolset,
        fetch_alarm_record_toolset,
        fetch_spwork_area_risk_toolset,
        fetch_major_hazard_sources_info_toolset,
    ],
    instruction = root_agent_instruction,
    output_key = 'Coordinator_report'
)




# root_agent = Agent(
#     name="smart_park_assistant",
#     model=LiteLlm(model="openai/qwen-plus"),
#     description=(
#         "作为智慧园区的综合助手，与用户对话并从中分析用户的意图，选择合适的Agent或工具来满足用户的需求"
#     ),
#     instruction=(
#         "你是一位智慧园区的助手，在化工园区的管理工作中经验丰富。你能从用户的对话中获知用户的真实意图，并通过调动合适的Agent来完成用户的需求。"
#         "你熟悉智慧园区系统的各个子系统，包括特殊作业系统、视频子系统、园区基础信息系统等，能够熟练的操作相应的信息化系统以回答用户的问题。"
#         "智慧园区的各子系统仅能提供特定业务的信息和功能，不能互操作，但你可以提取公共信息（如企业名称、地点等）来帮助用户在不同子系统中进行查询。"
#         "分配特殊作业数据查询相关的任务给 特殊作业系统管理员 ，可以获得特殊作业活动的类型、状态、时间和地点等信息。但是其中不包含视频信息；"
#         "分配视频、摄像头相关的任务给 智园园区视频监管系统管理员，可以查询园区内各企业的摄像头信息、预览等，但不包含特殊作业的相关信息。但是特殊作业记录中的企业名称和作业地点可以作为查找作业视频的依据。"
#     ),
#     sub_agents=[
#         sw_system_agent,
#         video_system_agent
#     ],
#     tools=[
#         get_current_time,
#         # agent_tool.AgentTool(agent=sw_system_operator_agent),
#         # agent_tool.AgentTool(agent=video_system_operator_agent)
#     ],
# )