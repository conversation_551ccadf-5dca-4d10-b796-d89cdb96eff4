#!/usr/bin/env python3
"""
Agent 使用 MCP Resource 的实际演示
展示如何处理用户的具体查询：天辰化工7月23日的特殊作业、获取当前天气信息
"""

import asyncio
import json
import sqlite3
from pathlib import Path

class AgentMCPResourceDemo:
    """模拟Agent使用MCP Resource的演示"""
    
    def __init__(self):
        self.available_resources = {
            # 企业相关资源
            "enterprise://info/{enterprise_name}": "获取企业基础信息",
            "enterprise://alarms/{enterprise_name}": "获取企业报警记录",
            "enterprise://risks/{enterprise_name}": "获取企业风险评估",
            
            # 数据文件资源
            "data://file/job_reporting_info.md": "作业申报信息文件",
            "data://file/alarm_record.md": "报警记录文件",
            
            # 数据库查询资源
            "db://query/work_activities": "作业活动表",
            "db://query/enterprises": "企业表",
            
            # 分析统计资源
            "analytics://enterprise_summary": "企业综合分析",
            "analytics://risk_dashboard": "风险仪表板",
            
            # 外部API资源
            "weather://current/{location}": "当前天气信息",
            "external://api/{service}": "外部API服务"
        }
    
    async def analyze_user_request(self, user_request: str):
        """分析用户请求并选择合适的资源"""
        print(f"🤖 Agent分析用户请求: {user_request}")
        print("-" * 60)
        
        # 智能分析用户请求
        if "天辰化工" in user_request and ("特殊作业" in user_request or "作业" in user_request):
            if "7月23" in user_request or "7-23" in user_request:
                return await self.handle_specific_work_query(user_request)
        
        elif "天气" in user_request:
            return await self.handle_weather_query(user_request)
        
        elif any(company in user_request for company in ["天辰化工", "合全药业", "新阳科技"]):
            return await self.handle_enterprise_query(user_request)
        
        else:
            return await self.handle_general_query(user_request)
    
    async def handle_specific_work_query(self, user_request: str):
        """处理特定的作业查询"""
        print("📋 识别为：特定日期的特殊作业查询")
        print("🎯 推荐策略：使用数据库查询资源进行精确查询")
        
        # 选择资源URI
        resource_uri = "db://query/work_activities"
        print(f"🔗 选择资源: {resource_uri}")
        
        # 模拟MCP Resource调用
        result = await self.simulate_database_query(
            "SELECT * FROM work_activities WHERE enterprise_name LIKE '%天辰化工%' AND DATE(apply_time) = '2025-07-23'"
        )
        
        if result and "data" in result and result["data"]:
            print(f"✅ 查询成功，找到 {len(result['data'])} 条记录")
            
            for i, activity in enumerate(result["data"], 1):
                print(f"\n📋 作业 {i}:")
                print(f"   企业: {activity.get('enterprise_name', 'N/A')}")
                print(f"   内容: {activity.get('activity_content', 'N/A')}")
                print(f"   区域: {activity.get('work_area', 'N/A')}")
                print(f"   类型: {activity.get('work_type', 'N/A')}")
                print(f"   风险等级: {activity.get('risk_level', 'N/A')}")
                print(f"   申请时间: {activity.get('apply_time', 'N/A')}")
                print(f"   是否高风险: {'是' if activity.get('is_high_risk') else '否'}")
        else:
            print("❌ 未找到符合条件的特殊作业记录")
            
            # Fallback策略：使用文件资源
            print("\n🔄 尝试Fallback策略：使用文件资源")
            fallback_uri = "data://file/job_reporting_info.md"
            print(f"🔗 Fallback资源: {fallback_uri}")
            
            fallback_result = await self.simulate_file_resource("job_reporting_info.md")
            if fallback_result and "data" in fallback_result:
                # 在文件数据中查找天辰化工的记录
                tianchen_activities = [
                    activity for activity in fallback_result["data"]
                    if "天辰化工" in activity.get("enterprise_name", "")
                ]
                
                if tianchen_activities:
                    print(f"✅ 在文件中找到天辰化工的 {len(tianchen_activities)} 条作业记录")
                    for i, activity in enumerate(tianchen_activities[:3], 1):  # 只显示前3条
                        print(f"   {i}. {activity.get('activity_content', 'N/A')} - {activity.get('work_area', 'N/A')}")
                else:
                    print("❌ 文件中也未找到天辰化工的作业记录")
        
        return result
    
    async def handle_weather_query(self, user_request: str):
        """处理天气查询"""
        print("🌤️  识别为：天气信息查询")
        print("🎯 推荐策略：使用天气资源")
        
        # 提取位置信息
        location = "常州"  # 默认位置
        if "常州" in user_request:
            location = "常州"
        elif "北京" in user_request:
            location = "北京"
        elif "上海" in user_request:
            location = "上海"
        
        resource_uri = f"weather://current/{location}"
        print(f"🔗 选择资源: {resource_uri}")
        
        # 模拟天气资源调用
        result = await self.simulate_weather_resource(location)
        
        if result and "data" in result:
            weather = result["data"]
            print(f"✅ 获取天气信息成功")
            print(f"📍 位置: {weather.get('location', 'N/A')}")
            print(f"🌡️  温度: {weather.get('temperature', 'N/A')}")
            print(f"💧 湿度: {weather.get('humidity', 'N/A')}")
            print(f"💨 风速: {weather.get('wind_speed', 'N/A')}")
            print(f"☀️  天气: {weather.get('weather_condition', 'N/A')}")
            print(f"🌬️  空气质量: {weather.get('air_quality', 'N/A')}")
            print(f"⚠️  注意: {weather.get('note', '')}")
        else:
            print("❌ 获取天气信息失败")
        
        return result
    
    async def handle_enterprise_query(self, user_request: str):
        """处理企业查询"""
        print("🏢 识别为：企业信息查询")
        
        # 提取企业名称
        enterprise_name = "天辰化工"
        if "合全" in user_request:
            enterprise_name = "合全"
        elif "新阳" in user_request:
            enterprise_name = "新阳"
        elif "天辰" in user_request:
            enterprise_name = "天辰化工"
        
        # 根据查询类型选择资源
        if "报警" in user_request:
            resource_uri = f"enterprise://alarms/{enterprise_name}"
            print(f"🎯 推荐策略：查询企业报警资源")
        elif "风险" in user_request:
            resource_uri = f"enterprise://risks/{enterprise_name}"
            print(f"🎯 推荐策略：查询企业风险资源")
        else:
            resource_uri = f"enterprise://info/{enterprise_name}"
            print(f"🎯 推荐策略：查询企业基础信息资源")
        
        print(f"🔗 选择资源: {resource_uri}")
        
        # 模拟企业资源调用
        result = await self.simulate_enterprise_resource(enterprise_name, resource_uri)
        
        if result and "data" in result:
            print(f"✅ 查询成功")
            if "statistics" in result:
                print(f"📊 统计信息: {result['statistics']}")
        else:
            print("❌ 查询失败")
        
        return result
    
    async def handle_general_query(self, user_request: str):
        """处理一般查询"""
        print("❓ 识别为：一般查询")
        print("🎯 推荐策略：使用综合分析资源")
        
        resource_uri = "analytics://enterprise_summary"
        print(f"🔗 选择资源: {resource_uri}")
        
        result = await self.simulate_analytics_resource()
        
        if result and "data" in result:
            print(f"✅ 获取综合分析成功")
            print(f"📊 包含 {len(result['data'])} 家企业的分析数据")
        else:
            print("❌ 获取综合分析失败")
        
        return result
    
    # 模拟MCP Resource调用的方法
    async def simulate_database_query(self, query: str):
        """模拟数据库查询资源"""
        try:
            db_path = "./enterprise.db"
            if not Path(db_path).exists():
                return {"error": "数据库文件不存在"}
            
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            result = {
                "resource_type": "database_query",
                "query": query,
                "data": [dict(row) for row in rows],
                "count": len(rows)
            }
            
            conn.close()
            return result
            
        except Exception as e:
            return {"error": f"数据库查询失败: {str(e)}"}
    
    async def simulate_file_resource(self, filename: str):
        """模拟文件资源"""
        try:
            file_path = Path("./data") / filename
            if not file_path.exists():
                return {"error": f"文件不存在: {filename}"}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取JSON数据
            if '[' in content and ']' in content:
                start_idx = content.find('[')
                end_idx = content.rfind(']') + 1
                json_str = content[start_idx:end_idx]
                data = json.loads(json_str)
                
                return {
                    "resource_type": "data_file",
                    "filename": filename,
                    "data": data,
                    "count": len(data)
                }
            else:
                return {
                    "resource_type": "data_file",
                    "filename": filename,
                    "content": content
                }
                
        except Exception as e:
            return {"error": f"文件读取失败: {str(e)}"}
    
    async def simulate_weather_resource(self, location: str):
        """模拟天气资源"""
        return {
            "resource_type": "weather_info",
            "location": location,
            "data": {
                "location": location,
                "temperature": "25°C",
                "humidity": "65%",
                "wind_speed": "3.2 m/s",
                "weather_condition": "晴朗",
                "air_quality": "良好",
                "visibility": "10km",
                "pressure": "1013 hPa",
                "update_time": "2025-01-30T12:00:00Z",
                "note": "这是模拟数据，实际使用需要配置真实的天气API"
            }
        }
    
    async def simulate_enterprise_resource(self, enterprise_name: str, resource_uri: str):
        """模拟企业资源"""
        if "alarms" in resource_uri:
            return {
                "resource_type": "enterprise_alarms",
                "enterprise_name": enterprise_name,
                "data": [
                    {"id": 1, "alarm_content": "温度超限报警", "alarm_time": "2025-07-24 09:37:45"},
                    {"id": 2, "alarm_content": "压力异常报警", "alarm_time": "2025-07-24 07:37:45"}
                ],
                "statistics": {"total_alarms": 2, "valid_alarms": 2}
            }
        elif "risks" in resource_uri:
            return {
                "resource_type": "enterprise_risks",
                "enterprise_name": enterprise_name,
                "data": [
                    {"control_object": "PVC生产装置", "risk_level": "重大风险", "is_high_risk": True}
                ],
                "statistics": {"total_risks": 1, "high_risks": 1}
            }
        else:
            return {
                "resource_type": "enterprise_info",
                "enterprise_name": enterprise_name,
                "data": [
                    {"name": f"{enterprise_name}有限公司", "short_name": enterprise_name, "risk_level": "中风险"}
                ]
            }
    
    async def simulate_analytics_resource(self):
        """模拟分析资源"""
        return {
            "resource_type": "enterprise_summary",
            "data": [
                {"enterprise_name": "天辰化工有限公司", "alarm_count": 5, "high_risk_items": 2},
                {"enterprise_name": "常州合全药业有限公司", "alarm_count": 8, "high_risk_items": 1}
            ]
        }

async def main():
    """主演示函数"""
    print("🚀 Agent 使用 MCP Resource 实际演示")
    print("展示如何处理具体的用户查询")
    print("=" * 80)
    
    demo = AgentMCPResourceDemo()
    
    # 测试查询场景
    test_queries = [
        "查一下天辰化工7月23日的特殊作业",
        "获取当前天气信息",
        "查看天辰化工的报警记录",
        "分析合全药业的风险状况",
        "给我一个企业综合报告"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试场景 {i}:")
        await demo.analyze_user_request(query)
        print("\n" + "=" * 80)
    
    print("✅ 演示完成")
    
    print("\n📋 总结:")
    print("1. ✅ Agent能够智能分析用户请求")
    print("2. ✅ 自动选择合适的MCP Resource URI")
    print("3. ✅ 支持Fallback策略（数据库→文件）")
    print("4. ✅ 提供结构化的数据响应")
    print("5. ✅ 处理外部API资源（天气信息）")
    
    print("\n🎯 实际部署要点:")
    print("- 配置真实的天气API密钥")
    print("- 优化数据库查询性能")
    print("- 添加更多的企业名称映射")
    print("- 实现更复杂的日期解析逻辑")

if __name__ == "__main__":
    asyncio.run(main())
